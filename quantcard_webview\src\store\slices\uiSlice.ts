/**
 * 🎨 UI状态切片 - 管理界面状态、导航和模态管理
 * 支持场景切换、模态管理、选择状态和过滤器管理
 * 使用原生immutable更新模式
 */

import type { SliceCreator, UISlice } from '../types/store'
import type { GameScene } from '../../types/game'

// 🎯 创建UI切片
export const createUISlice: SliceCreator<UISlice> = (set, get) => ({
  // 🎨 初始状态
  currentScene: 'WorldMap',
  previousScene: undefined,
  modals: {
    parameterConfig: false,
    cardDetail: false,
    groupSettings: false
  },
  selectedCards: new Set<string>(),
  selectedGroup: null,
  filters: {
    rarity: [],
    category: [],
    availability: 'all'
  },
  loading: false,
  error: undefined,

  // 🚀 场景导航
  switchScene: async (scene: GameScene) => {
    const currentState = get()

    if (currentState.currentScene === scene) {
      return
    }

    // 设置加载状态
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 模拟场景加载时间
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 更新场景状态
      set({
        ...get(),
        previousScene: get().currentScene,
        currentScene: scene,
        loading: false,
        // 切换场景时清空选择状态
        selectedCards: new Set<string>(),
        selectedGroup: null
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '场景切换失败'
      set({
        ...get(),
        loading: false,
        error: errorMessage
      })
    }
  },

  // 🔙 返回上一个场景
  goBack: () => {
    const { previousScene } = get()
    if (previousScene) {
      get().switchScene(previousScene)
    }
  },

  // 💬 模态管理
  openModal: (modalName) => {
    const currentState = get()
    set({
      ...currentState,
      modals: {
        ...currentState.modals,
        [modalName]: true
      }
    })
  },

  closeModal: (modalName) => {
    const currentState = get()
    set({
      ...currentState,
      modals: {
        ...currentState.modals,
        [modalName]: false
      }
    })
  },

  // 🃏 卡牌选择管理
  selectCard: (cardId: string) => {
    const currentState = get()
    const newSelectedCards = new Set(currentState.selectedCards)
    newSelectedCards.add(cardId)
    
    set({
      ...currentState,
      selectedCards: newSelectedCards
    })
  },

  unselectCard: (cardId: string) => {
    const currentState = get()
    const newSelectedCards = new Set(currentState.selectedCards)
    newSelectedCards.delete(cardId)
    
    set({
      ...currentState,
      selectedCards: newSelectedCards
    })
  },

  clearSelection: () => {
    const currentState = get()
    set({
      ...currentState,
      selectedCards: new Set<string>()
    })
  },

  // 🏗️ 策略组选择
  setSelectedGroup: (groupId: string | null) => {
    const currentState = get()
    set({
      ...currentState,
      selectedGroup: groupId
    })
  },

  // 📊 过滤器管理
  updateFilters: (newFilters: Partial<UISlice['filters']>) => {
    const currentState = get()
    set({
      ...currentState,
      filters: {
        ...currentState.filters,
        ...newFilters
      }
    })
  },

  // 🔧 错误处理
  clearError: () => {
    const currentState = get()
    set({
      ...currentState,
      error: undefined
    })
  },

  // 🔄 重置UI状态
  resetUIState: () => {
    set({
      currentScene: 'WorldMap',
      previousScene: undefined,
      modals: {
        parameterConfig: false,
        cardDetail: false,
        groupSettings: false
      },
      selectedCards: new Set<string>(),
      selectedGroup: null,
      filters: {
        rarity: [],
        category: [],
        availability: 'all'
      },
      loading: false,
      error: undefined
    })
  }
})