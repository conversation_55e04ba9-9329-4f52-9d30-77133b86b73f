/**
 * 🎯 策略保存管理器 - Strategy Save Manager
 * 独立的策略保存功能组件，封装所有保存相关的逻辑
 */

import React, { useState, useCallback } from 'react';
import { useCardsState } from '../../../store/hooks/useCardsState';
import { createStrategyGroup } from '../../../services/api/strategy';
import type { StrategyGroup, StrategyCardInstance } from '../../../types/game';
import type { StrategyTypeConfig } from '../StrategyTypeSelector/types';

export interface SaveResult {
  success: boolean;
  groupId?: string;
  message: string;
  error?: string;
}

export interface StrategySaveManagerProps {
  currentGroup: StrategyGroup | null;
  strategyTypeConfig: StrategyTypeConfig;
  onSaveComplete: (result: SaveResult) => void;
  children: (props: {
    handleSave: (name: string, description: string) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
  }) => React.ReactNode;
}

/**
 * 策略保存管理器组件
 * 使用 render props 模式，提供保存功能给子组件
 */
export const StrategySaveManager: React.FC<StrategySaveManagerProps> = ({
  currentGroup,
  strategyTypeConfig,
  onSaveComplete,
  children
}) => {
  const { consumeCard, releaseTempCard } = useCardsState();
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const handleSave = useCallback(async (name: string, description: string) => {
    if (!currentGroup) {
      const error = '没有可保存的策略组';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    if (currentGroup.cards.length === 0) {
      const error = '策略组中没有卡牌，无法保存';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      // 1. 消费卡片并释放临时使用状态
      const cardConsumptionPromises = currentGroup.cards.map(async (card: StrategyCardInstance) => {
        const success = await consumeCard(card.template_id, 1);
        if (!success) {
          throw new Error(`消费卡片 ${card.template_id} 失败`);
        }
        // 释放临时使用状态
        releaseTempCard(card.template_id, 1);
        return card;
      });

      await Promise.all(cardConsumptionPromises);

      // 2. 构建保存载荷
      const payload = {
        name: name.trim(),
        description: description.trim(),
        group_type: strategyTypeConfig.groupType,
        timing_symbols: strategyTypeConfig.timingSymbols,
        kline_period: strategyTypeConfig.klinePeriod,
        cards: currentGroup.cards.map(c => ({ 
          parameters: c.parameters, 
          template_id: c.template_id 
        })),
        execution_mode: currentGroup.execution_mode,
        execution_config: { 
          execution_type: strategyTypeConfig.groupType === 'timing' ? 'continuous' : 'onetime' 
        }
      };

      // 3. 保存策略组
      const response = await createStrategyGroup(payload as any);
      const newGroupId = response?.data?.id || response?.id || '';

      if (!newGroupId) {
        throw new Error('保存成功但未获取到策略组ID');
      }

      const successMessage = `策略组保存成功！\n\n🆔 组ID：${newGroupId}\n📛 名称：${name}\n🎴 卡片数：${currentGroup.cards.length}\n⚙️ 执行模式：${currentGroup.execution_mode}\n🎯 类型：${strategyTypeConfig.groupType === 'timing' ? '择时' : '选股'}`;

      onSaveComplete({
        success: true,
        groupId: newGroupId,
        message: successMessage
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '保存失败';
      setSaveError(errorMessage);
      onSaveComplete({
        success: false,
        message: errorMessage,
        error: errorMessage
      });
    } finally {
      setIsSaving(false);
    }
  }, [currentGroup, strategyTypeConfig, consumeCard, releaseTempCard, onSaveComplete]);

  return (
    <>
      {children({
        handleSave,
        isSaving,
        saveError
      })}
    </>
  );
};

export default StrategySaveManager;
