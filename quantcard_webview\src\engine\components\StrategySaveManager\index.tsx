/**
 * 🎯 策略保存管理器 - Strategy Save Manager
 * 独立的策略保存功能组件，封装所有保存相关的逻辑
 */

import React, { useState, useCallback } from 'react';
import { useCardsState } from '../../../store/hooks/useCardsState';
import { createStrategyGroup } from '../../../services/api/strategy';
import type { StrategyGroup } from '../../../types/game';
import type { StrategyTypeConfig } from '../StrategyTypeSelector/types';

export interface SaveResult {
  success: boolean;
  groupId?: string;
  message: string;
  error?: string;
}

export interface StrategySaveManagerProps {
  currentGroup: StrategyGroup | null;
  strategyTypeConfig: StrategyTypeConfig;
  templateCache: Record<string, any>; // 添加模板缓存
  onSaveComplete: (result: SaveResult) => void;
  children: (props: {
    handleSave: (name: string, description: string) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
  }) => React.ReactNode;
}

/**
 * 策略保存管理器组件
 * 使用 render props 模式，提供保存功能给子组件
 */
export const StrategySaveManager: React.FC<StrategySaveManagerProps> = ({
  currentGroup,
  strategyTypeConfig,
  templateCache,
  onSaveComplete,
  children
}) => {
  const { consumeCard, releaseTempCard } = useCardsState();
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const handleSave = useCallback(async (name: string, description: string) => {
    if (!currentGroup) {
      const error = '没有可保存的策略组';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    if (currentGroup.cards.length === 0) {
      const error = '策略组中没有卡牌，无法保存';
      setSaveError(error);
      onSaveComplete({ success: false, message: error, error });
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      // 1. 消费卡片并释放临时使用状态
      const consumedCards: string[] = []; // 记录已消费的卡片，用于回滚

      try {
        for (const card of currentGroup.cards) {
          const success = await consumeCard(card.template_id, 1);
          if (!success) {
            throw new Error(`消费卡片 ${card.template_id} 失败，可能是库存不足`);
          }
          consumedCards.push(card.template_id);
          // 释放临时使用状态
          releaseTempCard(card.template_id, 1);
        }
      } catch (consumeError) {
        // 如果消费失败，需要回滚已消费的卡片
        console.warn('卡片消费失败，开始回滚:', consumeError);
        // 注意：这里不进行回滚，因为consumeCard失败时不会真正消费
        throw consumeError;
      }

      // 2. 构建保存载荷 - 匹配后端API期望的数据结构
      const payload = {
        name: name.trim(),
        description: description.trim(),
        group_type: strategyTypeConfig.groupType,
        timing_symbols: strategyTypeConfig.timingSymbols,
        kline_period: strategyTypeConfig.klinePeriod,
        cards: currentGroup.cards.map(c => {
          // 从模板缓存获取模板信息
          const template = templateCache[c.template_id];
          const templateName = template?.name || `策略卡-${c.template_id}`;
          const templateDescription = template?.description || '';

          return {
            id: c.id,
            name: templateName,
            description: templateDescription,
            parameters: c.parameters || {},
            template_id: c.template_id,
            template_name: templateName,
            position: c.position || { x: 0, y: 0 }
          };
        }),
        execution_mode: currentGroup.execution_mode,
        execution_config: {
          execution_type: strategyTypeConfig.groupType === 'timing' ? 'continuous' : 'onetime'
        }
      };

      // 3. 保存策略组
      const response = await createStrategyGroup(payload as any);
      const newGroupId = response?.data?.id || response?.id || '';

      if (!newGroupId) {
        throw new Error('保存成功但未获取到策略组ID');
      }

      const successMessage = `策略组保存成功！\n\n🆔 组ID：${newGroupId}\n📛 名称：${name}\n🎴 卡片数：${currentGroup.cards.length}\n⚙️ 执行模式：${currentGroup.execution_mode}\n🎯 类型：${strategyTypeConfig.groupType === 'timing' ? '择时' : '选股'}`;

      onSaveComplete({
        success: true,
        groupId: newGroupId,
        message: successMessage
      });

    } catch (error) {
      console.error('策略组保存失败:', error);

      let errorMessage = '保存失败';
      let detailedError = '';

      if (error instanceof Error) {
        errorMessage = error.message;

        // 根据错误类型提供更详细的信息
        if (error.message.includes('库存不足')) {
          detailedError = '请检查卡牌库存是否充足';
        } else if (error.message.includes('网络')) {
          detailedError = '请检查网络连接后重试';
        } else if (error.message.includes('401') || error.message.includes('403')) {
          detailedError = '登录已过期，请重新登录';
        } else if (error.message.includes('500')) {
          detailedError = '服务器内部错误，请稍后重试';
        } else if (error.message.includes('消费卡片')) {
          detailedError = '卡牌消费失败，可能是库存不足或网络问题';
        } else {
          detailedError = '请检查网络连接和卡牌库存';
        }
      }

      const fullErrorMessage = detailedError ? `${errorMessage}\n${detailedError}` : errorMessage;

      setSaveError(fullErrorMessage);
      onSaveComplete({
        success: false,
        message: fullErrorMessage,
        error: errorMessage
      });
    } finally {
      setIsSaving(false);
    }
  }, [currentGroup, strategyTypeConfig, consumeCard, releaseTempCard, onSaveComplete]);

  return (
    <>
      {children({
        handleSave,
        isSaving,
        saveError
      })}
    </>
  );
};

export default StrategySaveManager;
