import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * 游戏场景类型定义
 */
export type GameScene = 
  | 'WorldMap'      // 世界地图主页
  | 'Arena'         // 竞技场大厅  
  | 'Battle'        // 战斗场景
  | 'Collection'    // 卡牌收藏
  | 'Strategy'      // 策略编辑
  | 'Shop'          // 商店
  | 'Profile'       // 个人资料
  | 'Settings';     // 设置

/**
 * 场景转换动画类型
 */
export type SceneTransition = 
  | 'fade'          // 淡入淡出
  | 'slide-left'    // 向左滑动
  | 'slide-right'   // 向右滑动
  | 'slide-up'      // 向上滑动
  | 'slide-down'    // 向下滑动
  | 'zoom-in'       // 缩放进入
  | 'zoom-out'      // 缩放离开
  | 'flip'          // 翻转
  | 'wipe';         // 擦除效果

/**
 * 场景数据接口
 */
export interface SceneData {
  [key: string]: any;
}

/**
 * 场景历史记录
 */
export interface SceneHistory {
  scene: GameScene;
  data?: SceneData;
  timestamp: number;
}

/**
 * 场景管理器状态
 */
interface SceneManagerState {
  // 当前场景
  currentScene: GameScene;
  // 上一个场景
  previousScene?: GameScene;
  // 场景数据
  sceneData: SceneData;
  // 转换动画类型
  transitionType: SceneTransition;
  // 转换进行中标志
  isTransitioning: boolean;
  // 场景历史记录（最多保存10个）
  history: SceneHistory[];
  // 全局加载状态
  isLoading: boolean;
  // 错误状态
  error?: string;
  
  // Actions
  /**
   * 切换到指定场景
   */
  switchToScene: (
    scene: GameScene, 
    data?: SceneData, 
    transition?: SceneTransition
  ) => Promise<void>;
  
  /**
   * 返回上一个场景
   */
  goBack: () => Promise<void>;
  
  /**
   * 设置场景数据
   */
  setSceneData: (data: SceneData) => void;
  
  /**
   * 更新场景数据
   */
  updateSceneData: (partialData: Partial<SceneData>) => void;
  
  /**
   * 清除场景数据
   */
  clearSceneData: () => void;
  
  /**
   * 设置加载状态
   */
  setLoading: (loading: boolean) => void;
  
  /**
   * 设置错误状态
   */
  setError: (error?: string) => void;
  
  /**
   * 获取场景历史
   */
  getHistory: () => SceneHistory[];
  
  /**
   * 清除历史记录
   */
  clearHistory: () => void;
  
  /**
   * 预加载场景资源
   */
  preloadScene: (scene: GameScene) => Promise<void>;
}

/**
 * 场景管理器Store
 */
export const useSceneManager = create<SceneManagerState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      currentScene: 'WorldMap',
      previousScene: undefined,
      sceneData: {},
      transitionType: 'fade',
      isTransitioning: false,
      history: [],
      isLoading: false,
      error: undefined,
      
      // 切换场景
      switchToScene: async (scene, data = {}, transition = 'fade') => {
        const state = get();
        
        // 防止重复切换到相同场景
        if (state.currentScene === scene && !data) {
          return;
        }
        
        // 设置转换状态
        set({
          isTransitioning: true,
          error: undefined
        });
        
        try {
          // 记录历史
          const historyEntry: SceneHistory = {
            scene: state.currentScene,
            data: state.sceneData,
            timestamp: Date.now()
          };
          
          const newHistory = [...state.history, historyEntry];
          // 限制历史记录数量
          if (newHistory.length > 10) {
            newHistory.shift();
          }
          
          // 模拟异步转换延迟
          await new Promise(resolve => setTimeout(resolve, 100));
          
          // 更新场景状态
          set({
            previousScene: state.currentScene,
            currentScene: scene,
            sceneData: data,
            transitionType: transition,
            history: newHistory,
            isTransitioning: false
          });
          
          // 触发场景切换事件
          window.dispatchEvent(new CustomEvent('sceneChanged', {
            detail: { 
              from: state.currentScene,
              to: scene,
              data,
              transition
            }
          }));
          
        } catch (error) {
          console.error('Scene transition failed:', error);
          set({
            isTransitioning: false,
            error: error instanceof Error ? error.message : 'Scene transition failed'
          });
        }
      },
      
      // 返回上一个场景
      goBack: async () => {
        const state = get();
        const lastHistory = state.history[state.history.length - 1];
        
        if (!lastHistory) {
          console.warn('No previous scene to go back to');
          return;
        }
        
        const newHistory = state.history.slice(0, -1);
        
        set({
          isTransitioning: true
        });
        
        try {
          await new Promise(resolve => setTimeout(resolve, 100));
          
          set({
            previousScene: state.currentScene,
            currentScene: lastHistory.scene,
            sceneData: lastHistory.data || {},
            transitionType: 'slide-right', // 返回动画
            history: newHistory,
            isTransitioning: false
          });
          
          window.dispatchEvent(new CustomEvent('sceneChanged', {
            detail: { 
              from: state.currentScene,
              to: lastHistory.scene,
              data: lastHistory.data,
              transition: 'slide-right',
              isGoingBack: true
            }
          }));
          
        } catch (error) {
          console.error('Go back failed:', error);
          set({
            isTransitioning: false,
            error: error instanceof Error ? error.message : 'Go back failed'
          });
        }
      },
      
      // 设置场景数据
      setSceneData: (data) => {
        set({ sceneData: data });
      },
      
      // 更新场景数据
      updateSceneData: (partialData) => {
        const state = get();
        set({ 
          sceneData: { ...state.sceneData, ...partialData }
        });
      },
      
      // 清除场景数据
      clearSceneData: () => {
        set({ sceneData: {} });
      },
      
      // 设置加载状态
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
      
      // 设置错误状态
      setError: (error) => {
        set({ error });
      },
      
      // 获取历史记录
      getHistory: () => {
        return get().history;
      },
      
      // 清除历史记录
      clearHistory: () => {
        set({ history: [] });
      },
      
      // 预加载场景资源
      preloadScene: async (scene) => {
        console.log(`Preloading scene: ${scene}`);
        // 这里可以实现具体的资源预加载逻辑
        // 如：图片、音频、3D模型等
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }),
    {
      name: 'scene-manager',
      partialize: (state) => ({
        // 只持久化必要的状态
        currentScene: state.currentScene,
        sceneData: state.sceneData
      })
    }
  )
);

/**
 * 场景管理工具函数
 */
export class SceneManagerUtils {
  /**
   * 获取场景的显示名称
   */
  static getSceneDisplayName(scene: GameScene): string {
    const names: Record<GameScene, string> = {
      WorldMap: '世界地图',
      Arena: '竞技场',
      Battle: '战斗',
      Collection: '卡牌收藏',
      Strategy: '策略编辑',
      Shop: '商店',
      Profile: '个人资料',
      Settings: '设置'
    };
    return names[scene] || scene;
  }
  
  /**
   * 获取场景的图标
   */
  static getSceneIcon(scene: GameScene): string {
    const icons: Record<GameScene, string> = {
      WorldMap: '🗺️',
      Arena: '⚔️',
      Battle: '⚡',
      Collection: '🃏',
      Strategy: '📋',
      Shop: '🛒',
      Profile: '👤',
      Settings: '⚙️'
    };
    return icons[scene] || '📱';
  }
  
  /**
   * 检查场景是否可以访问
   */
  static canAccessScene(scene: GameScene): boolean {
    // 这里可以添加权限检查逻辑
    // 例如：检查用户是否已登录、是否达到某个等级等
    return true;
  }
  
  /**
   * 获取场景的默认转换动画
   */
  static getDefaultTransition(fromScene: GameScene, toScene: GameScene): SceneTransition {
    // 根据场景类型选择合适的转换动画
    if (fromScene === 'WorldMap') {
      return 'zoom-in';
    }
    if (toScene === 'WorldMap') {
      return 'zoom-out';
    }
    if (fromScene === 'Arena' && toScene === 'Battle') {
      return 'slide-left';
    }
    if (fromScene === 'Battle' && toScene === 'Arena') {
      return 'slide-right';
    }
    return 'fade';
  }
}

/**
 * React Hook 用于监听场景变化
 */
export function useSceneChange(callback: (event: CustomEvent) => void) {
  const { useEffect } = require('react');
  
  useEffect(() => {
    const handleSceneChange = (event: CustomEvent) => {
      callback(event);
    };
    
    window.addEventListener('sceneChanged', handleSceneChange as EventListener);
    
    return () => {
      window.removeEventListener('sceneChanged', handleSceneChange as EventListener);
    };
  }, [callback]);
}

// 导出类型供其他模块使用
export type { SceneManagerState };