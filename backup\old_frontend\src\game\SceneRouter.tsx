import React, { Suspense, lazy } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSceneManager, GameScene, SceneTransition } from '../store/sceneManager';
import './SceneRouter.scss';

// 懒加载场景组件
const WorldMapScene = lazy(() => import('../scenes/WorldMapScene'));
const ArenaScene = lazy(() => import('../scenes/ArenaScene'));  
const BattleScene = lazy(() => import('../scenes/BattleScene'));
const CollectionScene = lazy(() => import('../scenes/CollectionScene'));
const StrategyScene = lazy(() => import('../scenes/StrategyScene'));
const ShopScene = lazy(() => import('../scenes/ShopScene'));
const ProfileScene = lazy(() => import('../scenes/ProfileScene'));
const SettingsScene = lazy(() => import('../scenes/SettingsScene'));

/**
 * 场景加载组件
 */
const SceneLoader: React.FC = () => (
  <div className="scene-loader">
    <div className="loader-spinner">
      <div className="spinner-ring"></div>
      <div className="spinner-ring"></div>
      <div className="spinner-ring"></div>
    </div>
    <div className="loader-text">Loading Scene...</div>
  </div>
);

/**
 * 场景转换动画变体定义
 */
const getTransitionVariants = (transition: SceneTransition) => {
  const variants = {
    fade: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    },
    'slide-left': {
      initial: { x: '100%', opacity: 0 },
      animate: { x: 0, opacity: 1 },
      exit: { x: '-100%', opacity: 0 }
    },
    'slide-right': {
      initial: { x: '-100%', opacity: 0 },
      animate: { x: 0, opacity: 1 },
      exit: { x: '100%', opacity: 0 }
    },
    'slide-up': {
      initial: { y: '100%', opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: '-100%', opacity: 0 }
    },
    'slide-down': {
      initial: { y: '-100%', opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: '100%', opacity: 0 }
    },
    'zoom-in': {
      initial: { scale: 0.8, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 1.2, opacity: 0 }
    },
    'zoom-out': {
      initial: { scale: 1.2, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.8, opacity: 0 }
    },
    flip: {
      initial: { rotateY: -90, opacity: 0 },
      animate: { rotateY: 0, opacity: 1 },
      exit: { rotateY: 90, opacity: 0 }
    },
    wipe: {
      initial: { clipPath: 'circle(0% at 50% 50%)', opacity: 0 },
      animate: { clipPath: 'circle(150% at 50% 50%)', opacity: 1 },
      exit: { clipPath: 'circle(0% at 50% 50%)', opacity: 0 }
    }
  };
  
  return variants[transition] || variants.fade;
};

/**
 * 场景路由器组件
 * 根据当前场景状态渲染对应的场景组件，并处理场景间的转换动画
 */
export default function SceneRouter() {
  const { 
    currentScene, 
    transitionType, 
    isTransitioning,
    sceneData,
    error 
  } = useSceneManager();

  /**
   * 获取场景组件
   */
  const getSceneComponent = (scene: GameScene) => {
    const sceneComponents: Record<GameScene, React.LazyExoticComponent<React.ComponentType<any>>> = {
      WorldMap: WorldMapScene,
      Arena: ArenaScene,
      Battle: BattleScene,
      Collection: CollectionScene,
      Strategy: StrategyScene,
      Shop: ShopScene,
      Profile: ProfileScene,
      Settings: SettingsScene
    };
    
    return sceneComponents[scene];
  };

  /**
   * 渲染错误状态
   */
  if (error) {
    return (
      <div className="scene-error">
        <div className="error-content">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error}</div>
          <button 
            className="error-retry-btn"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const SceneComponent = getSceneComponent(currentScene);
  const transitionVariants = getTransitionVariants(transitionType);

  return (
    <div className="scene-router">
      {/* 转换遮罩层 */}
      {isTransitioning && (
        <div className="scene-transition-overlay">
          <div className="transition-indicator">
            <div className="indicator-spinner"></div>
          </div>
        </div>
      )}

      {/* 场景内容区域 */}
      <div className="scene-container">
        <AnimatePresence 
          mode="wait" 
          initial={false}
          onExitComplete={() => {
            // 场景退出完成后的回调
            console.log(`Scene ${currentScene} transition completed`);
          }}
        >
          <motion.div
            key={currentScene}
            className="scene-wrapper"
            initial="initial"
            animate="animate"
            exit="exit"
            variants={transitionVariants}
            transition={{
              duration: 0.5,
              ease: [0.4, 0, 0.2, 1], // cubic-bezier for smooth animation
              staggerChildren: 0.1
            }}
          >
            <Suspense fallback={<SceneLoader />}>
              <SceneComponent 
                sceneData={sceneData}
                onSceneDataChange={(data: any) => {
                  // 场景数据变化回调
                  console.log('Scene data changed:', data);
                }}
              />
            </Suspense>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* 场景调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="scene-debug-info">
          <div className="debug-item">
            <span>Current:</span> {currentScene}
          </div>
          <div className="debug-item">
            <span>Transition:</span> {transitionType}
          </div>
          <div className="debug-item">
            <span>Loading:</span> {isTransitioning ? 'Yes' : 'No'}
          </div>
          {Object.keys(sceneData).length > 0 && (
            <div className="debug-item">
              <span>Data:</span> {JSON.stringify(sceneData, null, 2)}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 场景路由器Hook - 提供场景导航功能
 */
export function useSceneNavigation() {
  const { switchToScene, goBack, currentScene } = useSceneManager();
  
  return {
    // 导航到指定场景
    navigateToScene: switchToScene,
    
    // 返回上一个场景
    goBack,
    
    // 获取当前场景
    getCurrentScene: () => currentScene,
    
    // 快捷导航方法
    goToWorldMap: () => switchToScene('WorldMap', {}, 'zoom-out'),
    goToArena: () => switchToScene('Arena', {}, 'slide-left'),
    goToBattle: (battleData?: any) => switchToScene('Battle', battleData, 'zoom-in'),
    goToCollection: () => switchToScene('Collection', {}, 'slide-up'),
    goToStrategy: (strategyData?: any) => switchToScene('Strategy', strategyData, 'fade'),
    goToShop: () => switchToScene('Shop', {}, 'slide-left'),
    goToProfile: () => switchToScene('Profile', {}, 'slide-up'),
    goToSettings: () => switchToScene('Settings', {}, 'slide-down'),
  };
}