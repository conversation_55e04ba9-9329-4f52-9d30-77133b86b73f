import React from 'react';
import { useSceneNavigation } from '../SceneRouter';

export default function ShopScene({ sceneData }: { sceneData?: any }) {
  const navigation = useSceneNavigation();

  return (
    <div style={{ padding: '2rem', textAlign: 'center', color: 'white' }}>
      <h1>🛒 Shop</h1>
      <p>Buy new strategy cards and items</p>
      <button 
        onClick={() => navigation.goToWorldMap()}
        style={{ padding: '12px 24px', marginTop: '2rem', background: 'transparent', color: '#00ffff', border: '2px solid #00ffff', borderRadius: '25px', cursor: 'pointer' }}
      >
        ← Back to Map
      </button>
    </div>
  );
}