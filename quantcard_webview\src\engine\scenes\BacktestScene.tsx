/**
 * 📈 回测实验室场�?- 深色主题
 * 集成策略回测、性能分析�?D图表可视�?
 */

import React, { useState, useMemo, useEffect } from 'react'
import styled from 'styled-components'
import { useUIState } from '../../store/hooks'
import { useTheme } from '../../styles/themes/ThemeProvider'
import PerformanceChart3D from '../components/PerformanceChart3D/PerformanceChart3D'
import CyberButton from '../components/CyberButton'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

// 📋 内容区域
const Content = styled.div<{ theme: any }>`
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const Card = styled.div<{ theme: any }>`
  background: ${p => p.theme.colors.background.card};
  border: 1px solid ${p => p.theme.colors.border.primary};
  border-radius: ${p => p.theme.spacing.borderRadius.lg};
  padding: 1rem;
  box-shadow: ${p => p.theme.shadows.base};
`

const Row = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
`

const Label = styled.label<{ theme: any }>`
  display: block;
  font-size: ${p => p.theme.typography.sizes.sm};
  color: ${p => p.theme.colors.text.secondary};
  margin-bottom: 0.5rem;
  font-family: ${p => p.theme.typography.fonts.primary};
  font-weight: 600;
`

const Input = styled.input<{ theme: any }>`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${p => p.theme.colors.border.secondary};
  border-radius: 8px;
  background: ${p => p.theme.colors.background.surface};
  color: ${p => p.theme.colors.text.primary};
  font-family: ${p => p.theme.typography.fonts.primary};
  
  &:focus {
    outline: none;
    border-color: ${p => p.theme.colors.primaryColors.cyan};
    box-shadow: 0 0 0 2px ${p => p.theme.colors.primaryColors.cyan}20;
  }
`

const Select = styled.select<{ theme: any }>`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid ${p => p.theme.colors.border.secondary};
  border-radius: 8px;
  background: ${p => p.theme.colors.background.surface};
  color: ${p => p.theme.colors.text.primary};
  font-family: ${p => p.theme.typography.fonts.primary};
  
  &:focus {
    outline: none;
    border-color: ${p => p.theme.colors.primaryColors.cyan};
    box-shadow: 0 0 0 2px ${p => p.theme.colors.primaryColors.cyan}20;
  }
`

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  
  @media (max-width: 640px) {
    flex-direction: column;
  }
`

export default function BacktestScene() {
  const { switchScene } = useUIState()
  const { setMode } = useTheme()

  // 进入即切到深色主�?
  useEffect(() => { setMode('dark') }, [setMode])

  const [params, setParams] = useState({ 
    symbol: '000001.SZ', 
    timeframe: 'D', 
    start: '2023-01-01', 
    end: '2023-12-31' 
  })

  const mockData = useMemo(() => {
    const n = 120
    const timestamp = Array.from({ length: n }, (_, i) => new Date(2023, 0, 1 + i))
    const equity: number[] = []
    const returns: number[] = []
    const drawdown: number[] = []
    const volatility: number[] = []
    let eq = 100, peak = 100
    for (let i = 0; i < n; i++) {
      const r = (Math.sin(i / 8) + Math.cos(i / 5)) * 0.3 + (Math.random() - 0.5) * 0.5
      const ret = r / 100
      eq = eq * (1 + ret)
      peak = Math.max(peak, eq)
      equity.push(eq); returns.push(ret); drawdown.push(((eq - peak) / peak) * 100); volatility.push(Math.abs(r))
    }
    return { timestamp, equity, returns, drawdown, volatility }
  }, [params])

  return (
    <UnifiedMobileNav title="回测实验室" bottomNavActive="Home">
      <Content>
        <Card>
          <h3 style={{ margin: '0 0 1rem 0', color: 'inherit' }}>📊 回测参数</h3>
          <Row>
            <div>
              <Label>交易标的</Label>
              <Select 
                value={params.symbol} 
                onChange={e => setParams({ ...params, symbol: e.target.value })}
              >
                <option value="000001.SZ">平安银行 (000001.SZ)</option>
                <option value="600000.SH">浦发银行 (600000.SH)</option>
                <option value="000002.SZ">万科A (000002.SZ)</option>
                <option value="600036.SH">招商银行 (600036.SH)</option>
              </Select>
            </div>
            <div>
              <Label>时间周期</Label>
              <Select 
                value={params.timeframe} 
                onChange={e => setParams({ ...params, timeframe: e.target.value })}
              >
                <option value="D">日线 (D)</option>
                <option value="W">周线 (W)</option>
                <option value="60m">60分钟 (60m)</option>
                <option value="30m">30分钟 (30m)</option>
                <option value="15m">15分钟 (15m)</option>
              </Select>
            </div>
          </Row>
          <Row style={{ marginTop: '1rem' }}>
            <div>
              <Label>开始日期</Label>
              <Input 
                type="date" 
                value={params.start} 
                onChange={e => setParams({ ...params, start: e.target.value })} 
              />
            </div>
            <div>
              <Label>结束日期</Label>
              <Input 
                type="date" 
                value={params.end} 
                onChange={e => setParams({ ...params, end: e.target.value })} 
              />
            </div>
          </Row>
          <ButtonGroup>
            <CyberButton variant="primary" simplified>
              🚀 运行回测
            </CyberButton>
            <CyberButton variant="secondary" simplified>
              📋 导出报告
            </CyberButton>
                         <CyberButton variant="ghost" simplified>
               ⚙️ 高级设置
             </CyberButton>
          </ButtonGroup>
        </Card>
        
        <Card>
          <h3 style={{ margin: '0 0 1rem 0', color: 'inherit' }}>📈 性能分析</h3>
          <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
            <PerformanceChart3D 
              data={mockData} 
              width={Math.min(860, window.innerWidth - 48)} 
              height={Math.round((window.innerHeight * 0.5))} 
            />
          </div>
        </Card>
      </Content>
    </UnifiedMobileNav>
  )
} 
