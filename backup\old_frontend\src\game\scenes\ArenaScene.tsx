import React from 'react';
import { useSceneNavigation } from '../SceneRouter';
import './ArenaScene.scss';

export default function ArenaScene({ sceneData }: { sceneData?: any }) {
  const navigation = useSceneNavigation();

  return (
    <div className="arena-scene">
      <div className="arena-content">
        <h1 className="arena-title">🏟️ Arena</h1>
        <p className="arena-subtitle">Choose your battle mode</p>
        
        <div className="battle-modes">
          <div className="mode-card" onClick={() => navigation.goToBattle()}>
            <div className="mode-icon">⚔️</div>
            <div className="mode-name">Quick Battle</div>
            <div className="mode-desc">Fast PvP match</div>
          </div>
          
          <div className="mode-card">
            <div className="mode-icon">🏆</div>
            <div className="mode-name">Tournament</div>
            <div className="mode-desc">Ranked competition</div>
          </div>
          
          <div className="mode-card">
            <div className="mode-icon">🎯</div>
            <div className="mode-name">Practice</div>
            <div className="mode-desc">AI training</div>
          </div>
        </div>
        
        <button className="back-btn" onClick={() => navigation.goToWorldMap()}>
          ← Back to Map
        </button>
      </div>
    </div>
  );
}