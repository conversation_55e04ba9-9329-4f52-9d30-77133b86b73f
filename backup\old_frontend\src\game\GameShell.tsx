import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './GameShell.scss';

interface GameShellProps {
  children: React.ReactNode;
  className?: string;
  showBackground?: boolean;
}

/**
 * GameShell - 游戏全屏容器组件
 * 
 * 功能：
 * 1. 全屏显示，禁用页面滚动
 * 2. 自适应安全区域（移动端）
 * 3. Canvas层承载游戏内容，HUD层承载React DOM UI
 * 4. 统一的手势和触控处理
 */
export default function GameShell({ children, className = '', showBackground = true }: GameShellProps) {
  const shellRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 禁用页面滚动和缩放
    const disableScroll = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const disableTouch = (e: TouchEvent) => {
      // 防止双指缩放
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    // 禁用右键菜单
    const disableContextMenu = (e: Event) => {
      e.preventDefault();
    };

    // 添加事件监听
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.height = '100%';
    
    document.addEventListener('wheel', disableScroll, { passive: false });
    document.addEventListener('touchmove', disableScroll, { passive: false });
    document.addEventListener('touchstart', disableTouch, { passive: false });
    document.addEventListener('contextmenu', disableContextMenu);

    // 处理移动端viewport
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute(
        'content',
        'width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover'
      );
    }

    // 清理函数
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';
      
      document.removeEventListener('wheel', disableScroll);
      document.removeEventListener('touchmove', disableScroll);
      document.removeEventListener('touchstart', disableTouch);
      document.removeEventListener('contextmenu', disableContextMenu);
    };
  }, []);

  return (
    <div 
      ref={shellRef}
      className={`game-shell ${className}`}
      data-game-shell="true"
    >
      {/* 游戏背景层 */}
      {showBackground && (
        <div className="game-shell__background">
          {/* 科技风格网格背景 */}
          <div className="background-grid" />
          {/* 粒子效果背景 */}
          <div className="background-particles" />
          {/* 渐变蒙层 */}
          <div className="background-overlay" />
        </div>
      )}

      {/* 游戏内容层 */}
      <div className="game-shell__content">
        <AnimatePresence mode="wait">
          <motion.div
            className="game-shell__scene"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* HUD层 - 始终在最上层 */}
      <div 
        id="game-hud" 
        className="game-shell__hud"
        style={{ pointerEvents: 'none' }}
      >
        {/* HUD内容将由具体场景注入 */}
      </div>

      {/* 全局游戏控制HUD */}
      <div className="game-shell__controls">
        <button 
          className="control-btn control-btn--settings"
          onClick={() => console.log('Settings clicked')}
        >
          ⚙️
        </button>
        <button 
          className="control-btn control-btn--fullscreen"
          onClick={() => {
            if (!document.fullscreenElement) {
              document.documentElement.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          }}
        >
          📱
        </button>
      </div>
    </div>
  );
}