import { Howl, Howler } from 'howler';

/**
 * 音效类型定义
 */
export type SoundType = 
  | 'ui_click'
  | 'ui_hover' 
  | 'ui_success'
  | 'ui_error'
  | 'ui_notification'
  | 'card_flip'
  | 'card_play'
  | 'card_draw'
  | 'battle_hit'
  | 'battle_win'
  | 'battle_lose'
  | 'ambient_background'
  | 'ambient_battle'
  | 'ambient_menu';

/**
 * 音效配置接口
 */
interface SoundConfig {
  src: string[];
  volume?: number;
  loop?: boolean;
  preload?: boolean;
  pool?: number;
}

/**
 * 音效管理器类
 */
class AudioManager {
  private sounds: Map<SoundType, Howl> = new Map();
  private enabled: boolean = true;
  private masterVolume: number = 1.0;
  private soundVolume: number = 0.7;
  private musicVolume: number = 0.5;
  private currentBgMusic: Howl | null = null;

  /**
   * 音效资源配置
   */
  private soundConfigs: Record<SoundType, SoundConfig> = {
    // UI 音效
    ui_click: {
      src: ['/sounds/ui/click.mp3', '/sounds/ui/click.wav'],
      volume: 0.6,
      pool: 5
    },
    ui_hover: {
      src: ['/sounds/ui/hover.mp3', '/sounds/ui/hover.wav'],
      volume: 0.3,
      pool: 3
    },
    ui_success: {
      src: ['/sounds/ui/success.mp3', '/sounds/ui/success.wav'],
      volume: 0.8
    },
    ui_error: {
      src: ['/sounds/ui/error.mp3', '/sounds/ui/error.wav'],
      volume: 0.8
    },
    ui_notification: {
      src: ['/sounds/ui/notification.mp3', '/sounds/ui/notification.wav'],
      volume: 0.7
    },
    
    // 卡牌音效
    card_flip: {
      src: ['/sounds/card/flip.mp3', '/sounds/card/flip.wav'],
      volume: 0.5,
      pool: 5
    },
    card_play: {
      src: ['/sounds/card/play.mp3', '/sounds/card/play.wav'],
      volume: 0.8,
      pool: 3
    },
    card_draw: {
      src: ['/sounds/card/draw.mp3', '/sounds/card/draw.wav'],
      volume: 0.6,
      pool: 3
    },
    
    // 战斗音效
    battle_hit: {
      src: ['/sounds/battle/hit.mp3', '/sounds/battle/hit.wav'],
      volume: 0.9,
      pool: 10
    },
    battle_win: {
      src: ['/sounds/battle/win.mp3', '/sounds/battle/win.wav'],
      volume: 1.0
    },
    battle_lose: {
      src: ['/sounds/battle/lose.mp3', '/sounds/battle/lose.wav'],
      volume: 1.0
    },
    
    // 背景音乐
    ambient_background: {
      src: ['/sounds/ambient/background.mp3', '/sounds/ambient/background.ogg'],
      volume: 0.4,
      loop: true,
      preload: true
    },
    ambient_battle: {
      src: ['/sounds/ambient/battle.mp3', '/sounds/ambient/battle.ogg'],
      volume: 0.6,
      loop: true,
      preload: true
    },
    ambient_menu: {
      src: ['/sounds/ambient/menu.mp3', '/sounds/ambient/menu.ogg'],
      volume: 0.3,
      loop: true,
      preload: true
    }
  };

  constructor() {
    this.initializeAudio();
    this.loadFromStorage();
  }

  /**
   * 初始化音频设置
   */
  private initializeAudio() {
    // 设置全局音频
    Howler.autoUnlock = true;
    Howler.html5PoolSize = 10;
    
    // 优化的页面可见性监听（防止性能问题）
    let visibilityTimer: NodeJS.Timeout | null = null;
    document.addEventListener('visibilitychange', () => {
      // 使用防抖避免频繁切换
      if (visibilityTimer) {
        clearTimeout(visibilityTimer);
      }
      
      visibilityTimer = setTimeout(() => {
        if (document.hidden) {
          this.pauseAll();
        } else {
          this.resumeAll();
        }
        visibilityTimer = null;
      }, 100);
    });

    // 监听音频上下文状态
    if (typeof window !== 'undefined') {
      const handleFirstInteraction = () => {
        if (Howler.ctx && Howler.ctx.state === 'suspended') {
          Howler.ctx.resume().catch(console.warn);
        }
        document.removeEventListener('touchstart', handleFirstInteraction, { passive: true });
        document.removeEventListener('click', handleFirstInteraction);
      };

      document.addEventListener('touchstart', handleFirstInteraction, { passive: true });
      document.addEventListener('click', handleFirstInteraction);
    }
  }

  /**
   * 预加载所有音效
   */
  async preloadSounds(): Promise<void> {
    const promises: Promise<void>[] = [];

    Object.entries(this.soundConfigs).forEach(([soundType, config]) => {
      if (config.preload !== false) {
        promises.push(this.loadSound(soundType as SoundType, config));
      }
    });

    await Promise.all(promises);
    console.log('Audio Manager: All sounds preloaded');
  }

  /**
   * 加载单个音效
   */
  private loadSound(soundType: SoundType, config: SoundConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const sound = new Howl({
        src: config.src,
        volume: (config.volume || 1) * this.getSoundTypeVolume(soundType) * this.masterVolume,
        loop: config.loop || false,
        pool: config.pool || 1,
        preload: config.preload !== false,
        onload: () => {
          this.sounds.set(soundType, sound);
          resolve();
        },
        onloaderror: (id, error) => {
          console.warn(`Failed to load sound: ${soundType}`, error);
          resolve(); // 继续加载其他音效
        }
      });
    });
  }

  /**
   * 播放音效
   */
  play(soundType: SoundType, options?: {
    volume?: number;
    rate?: number;
    loop?: boolean;
    fade?: { from: number; to: number; duration: number };
  }): number | null {
    if (!this.enabled) return null;

    let sound = this.sounds.get(soundType);
    
    // 如果音效未加载，尝试立即加载
    if (!sound) {
      const config = this.soundConfigs[soundType];
      if (config) {
        this.loadSound(soundType, config);
        sound = this.sounds.get(soundType);
      }
    }

    if (!sound) {
      console.warn(`Sound not found: ${soundType}`);
      return null;
    }

    try {
      const soundId = sound.play();
      
      if (options) {
        if (options.volume !== undefined) {
          sound.volume(options.volume * this.getSoundTypeVolume(soundType) * this.masterVolume, soundId);
        }
        
        if (options.rate !== undefined) {
          sound.rate(options.rate, soundId);
        }
        
        if (options.loop !== undefined) {
          sound.loop(options.loop, soundId);
        }
        
        if (options.fade) {
          sound.fade(options.fade.from, options.fade.to, options.fade.duration, soundId);
        }
      }

      return soundId;
    } catch (error) {
      console.error(`Error playing sound: ${soundType}`, error);
      return null;
    }
  }

  /**
   * 停止音效
   */
  stop(soundType: SoundType, soundId?: number): void {
    const sound = this.sounds.get(soundType);
    if (sound) {
      sound.stop(soundId);
    }
  }

  /**
   * 暂停音效
   */
  pause(soundType: SoundType, soundId?: number): void {
    const sound = this.sounds.get(soundType);
    if (sound) {
      sound.pause(soundId);
    }
  }

  /**
   * 恢复音效
   */
  resume(soundType: SoundType, soundId?: number): void {
    const sound = this.sounds.get(soundType);
    if (sound) {
      sound.play(soundId);
    }
  }

  /**
   * 播放背景音乐
   */
  playBackgroundMusic(soundType: SoundType): void {
    if (this.currentBgMusic) {
      this.currentBgMusic.fade(this.currentBgMusic.volume(), 0, 1000);
      setTimeout(() => {
        if (this.currentBgMusic) {
          this.currentBgMusic.stop();
        }
      }, 1000);
    }

    const sound = this.sounds.get(soundType);
    if (sound) {
      sound.volume(0);
      const soundId = sound.play();
      sound.fade(0, this.musicVolume * this.masterVolume, 2000, soundId);
      this.currentBgMusic = sound;
    }
  }

  /**
   * 停止背景音乐
   */
  stopBackgroundMusic(): void {
    if (this.currentBgMusic) {
      this.currentBgMusic.fade(this.currentBgMusic.volume(), 0, 1000);
      setTimeout(() => {
        if (this.currentBgMusic) {
          this.currentBgMusic.stop();
          this.currentBgMusic = null;
        }
      }, 1000);
    }
  }

  /**
   * 暂停所有音效
   */
  pauseAll(): void {
    Howler.mute(true);
  }

  /**
   * 恢复所有音效
   */
  resumeAll(): void {
    Howler.mute(false);
  }

  /**
   * 获取音效类型对应的音量
   */
  private getSoundTypeVolume(soundType: SoundType): number {
    if (soundType.startsWith('ambient_')) {
      return this.musicVolume;
    }
    return this.soundVolume;
  }

  /**
   * 设置主音量
   */
  setMasterVolume(volume: number): void {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
    this.saveToStorage();
  }

  /**
   * 设置音效音量
   */
  setSoundVolume(volume: number): void {
    this.soundVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
    this.saveToStorage();
  }

  /**
   * 设置音乐音量
   */
  setMusicVolume(volume: number): void {
    this.musicVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
    this.saveToStorage();
  }

  /**
   * 更新所有音效音量
   */
  private updateAllVolumes(): void {
    this.sounds.forEach((sound, soundType) => {
      const config = this.soundConfigs[soundType];
      const baseVolume = config.volume || 1;
      const typeVolume = this.getSoundTypeVolume(soundType);
      sound.volume(baseVolume * typeVolume * this.masterVolume);
    });
  }

  /**
   * 启用/禁用音效
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    if (!enabled) {
      this.pauseAll();
    } else {
      this.resumeAll();
    }
    this.saveToStorage();
  }

  /**
   * 保存设置到本地存储
   */
  private saveToStorage(): void {
    if (typeof window !== 'undefined') {
      const settings = {
        enabled: this.enabled,
        masterVolume: this.masterVolume,
        soundVolume: this.soundVolume,
        musicVolume: this.musicVolume
      };
      localStorage.setItem('quantcard_audio_settings', JSON.stringify(settings));
    }
  }

  /**
   * 从本地存储加载设置
   */
  private loadFromStorage(): void {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('quantcard_audio_settings');
      if (saved) {
        try {
          const settings = JSON.parse(saved);
          this.enabled = settings.enabled ?? true;
          this.masterVolume = settings.masterVolume ?? 1.0;
          this.soundVolume = settings.soundVolume ?? 0.7;
          this.musicVolume = settings.musicVolume ?? 0.5;
        } catch (error) {
          console.warn('Failed to load audio settings:', error);
        }
      }
    }
  }

  /**
   * 获取当前设置
   */
  getSettings() {
    return {
      enabled: this.enabled,
      masterVolume: this.masterVolume,
      soundVolume: this.soundVolume,
      musicVolume: this.musicVolume
    };
  }

  /**
   * 销毁音频管理器
   */
  destroy(): void {
    this.sounds.forEach(sound => sound.unload());
    this.sounds.clear();
    this.currentBgMusic = null;
  }
}

// 创建全局音频管理器实例
export const audioManager = new AudioManager();

// 快捷方法
export const playSound = (soundType: SoundType, options?: any) => {
  return audioManager.play(soundType, options);
};

export const playBgMusic = (soundType: SoundType) => {
  audioManager.playBackgroundMusic(soundType);
};

export const stopBgMusic = () => {
  audioManager.stopBackgroundMusic();
};

// 预加载音效
export const preloadAudio = () => {
  return audioManager.preloadSounds();
};

export default audioManager;