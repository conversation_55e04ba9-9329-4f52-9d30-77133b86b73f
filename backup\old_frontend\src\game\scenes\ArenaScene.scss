@use "../../styles/themes/cyber-theme" as cyber;

.arena-scene {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  
  .arena-content {
    text-align: center;
    color: white;
    
    .arena-title {
      font-size: 4rem;
      margin-bottom: 1rem;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    }
    
    .arena-subtitle {
      font-size: 1.5rem;
      margin-bottom: 3rem;
      color: rgba(255, 255, 255, 0.8);
    }
    
    .battle-modes {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
      
      .mode-card {
        background: rgba(0, 0, 0, 0.5);
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 15px;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #00ffff;
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
        }
        
        .mode-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }
        
        .mode-name {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          color: #00ffff;
        }
        
        .mode-desc {
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
    
    .back-btn {
      padding: 12px 24px;
      background: transparent;
      color: #00ffff;
      border: 2px solid #00ffff;
      border-radius: 25px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(0, 255, 255, 0.1);
        transform: translateY(-2px);
      }
    }
  }
}