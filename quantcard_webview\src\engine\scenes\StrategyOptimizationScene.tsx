/**
 * 🧬 策略优化场景 - QuantCard Strategy Lab
 * 集成数据源、智能调优、图表可视化、全面分析的游戏化界面
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUIState } from '../../store/hooks';
import { useTheme } from '../../styles/themes/ThemeProvider'

import type { StrategyTemplate, PerformanceMetrics } from '../../types/game'
import CyberButton from '../components/CyberButton';
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

interface StrategyConfig extends StrategyTemplate {
  type: 'filter' | 'timing' | 'backtest';
  performance?: PerformanceMetrics;
}

interface DataSourceConfig {
  mode: 'filter' | 'timing' | 'backtest';
  sources: { realtime: boolean; historical: boolean; fundamental: boolean; technical: boolean };
  timeframe: { start: Date; end: Date; interval: '1min' | '5min' | '15min' | '30min' | '1h' | '1d' };
  stockPool: { symbols: string[]; dynamicFiltering: boolean };
}

interface OptimizationJob {
  id: string;
  strategy: StrategyConfig | null;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  results?: PerformanceMetrics[];
}

// 容器（浅色主题，移动优先）
const Page = styled.div<{ theme: any }>`
  min-height: 100vh;
  background: ${p => p.theme.colors.background.void};
  color: ${p => p.theme.colors.text.primary};
  display: flex;
  flex-direction: column;
`

// 内容栅格（移动端单列，平板/桌面多列）
const Grid = styled.div<{ theme: any }>`
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  padding: 12px;
  padding-bottom: 72px; /* 预留底部导航 */

  ${p => p.theme.breakpoints.up(p.theme.breakpoints.md)} {
    grid-template-columns: 1fr 1fr;
  }
  
  ${p => p.theme.breakpoints.up(p.theme.breakpoints.lg)} {
    grid-template-columns: 1.2fr 1fr 1fr;
  }
`

const Card = styled(motion.div)<{ theme: any }>`
  background: ${p => p.theme.colors.background.card};
  border: 1px solid ${p => p.theme.colors.border.secondary};
  border-radius: ${p => p.theme.spacing.borderRadius.lg};
  box-shadow: ${p => p.theme.shadows.base};
  padding: 12px;
`

const SectionTitle = styled.h3<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: ${p => p.theme.typography.fonts.gaming};
  font-size: ${p => p.theme.typography.sizes.lg};
  color: ${p => p.theme.colors.primaryColors.cyan};
  margin: 0 0 8px 0;
`

const FieldLabel = styled.label<{ theme: any }>`
  display: block;
  font-size: ${p => p.theme.typography.sizes.sm};
  color: ${p => p.theme.colors.text.secondary};
  margin: 6px 0 4px;
`

const Select = styled.select<{ theme: any }>`
  width: 100%; height: 36px;
  border-radius: 8px;
  border: 1px solid ${p => p.theme.colors.border.secondary};
  background: ${p => p.theme.colors.background.surface};
  color: ${p => p.theme.colors.text.primary};
  padding: 0 10px;
`

const CheckboxRow = styled.div<{ theme: any }>`
  display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;
  span { display: inline-flex; gap: 6px; align-items: center; color: ${p => p.theme.colors.text.secondary}; }
  input { accent-color: ${p => p.theme.colors.primaryColors.cyan}; }
`

const ChipRow = styled.div<{ theme: any }>`
  display: flex; flex-wrap: wrap; gap: 6px;
  .chip {
    padding: 4px 8px;
    background: ${p => p.theme.colors.primaryColors.cyan}14;
    border: 1px solid ${p => p.theme.colors.primaryColors.cyan}40;
    border-radius: 999px;
    color: ${p => p.theme.colors.primaryColors.cyan};
    font-size: ${p => p.theme.typography.sizes.xs};
  }
`

const KpiGrid = styled.div<{ theme: any }>`
  display: grid; gap: 8px; grid-template-columns: repeat(2, 1fr);
  ${p => p.theme.breakpoints.up(p.theme.breakpoints.md)} { grid-template-columns: repeat(4, 1fr); }
`

const KpiCard = styled(Card)`
  padding: 10px;
  .val { font-family: "Orbitron", monospace; font-size: 1.1rem; font-weight: 700 }
  .lbl { font-size: 0.78rem; opacity: 0.7 }
`

export default function StrategyOptimizationScene() {
  const { switchScene } = useUIState();
  const { switchTheme } = useTheme()

  // 进入即切到浅色主题
  useEffect(() => { switchTheme('StrategyOptimization') }, [switchTheme])
  
  const [selectedStrategy, setSelectedStrategy] = useState<StrategyConfig | null>(null);
  const [dataSource, setDataSource] = useState<DataSourceConfig>({
    mode: 'filter',
    sources: { realtime: true, historical: true, fundamental: false, technical: true },
    timeframe: { start: new Date(Date.now() - 30*24*3600*1000), end: new Date(), interval: '5min' },
    stockPool: { symbols: ['AAPL','TSLA','NVDA','MSFT'], dynamicFiltering: true }
  });
  
  const [optimization, setOptimization] = useState<OptimizationJob>({ id: '', strategy: null, status: 'idle', progress: 0 });
  
  const mockMetrics: PerformanceMetrics = { total_return: 24.67, win_rate: 67.2, max_drawdown: -8.2, sharpe_ratio: 1.34, volatility: 16.7, alpha: 0.5, beta: 1.12 };

  const handleStartOptimization = useCallback(() => {
    setOptimization({ id: 'job', strategy: selectedStrategy, status: 'running', progress: 0 })
    const timer = setInterval(() => {
      setOptimization(prev => {
        if (prev.progress >= 100) { clearInterval(timer); return { ...prev, status: 'completed', progress: 100 } }
        return { ...prev, progress: Math.min(100, prev.progress + 8 + Math.random()*6) }
      })
    }, 450)
  }, [selectedStrategy])

  const metricsData = useMemo(() => ([
    { label: 'Total Return', value: `${mockMetrics.total_return}%` },
    { label: 'Win Rate', value: `${mockMetrics.win_rate}%` },
    { label: 'Max Drawdown', value: `${mockMetrics.max_drawdown}%` },
    { label: 'Sharpe Ratio', value: mockMetrics.sharpe_ratio.toFixed(2) },
    { label: 'Volatility', value: `${mockMetrics.volatility}%` },
    { label: 'Alpha', value: mockMetrics.alpha.toFixed(2) }
  ]), [mockMetrics])

  return (
    <UnifiedMobileNav title="策略实验室" bottomNavActive="Home">
      <Grid>
        <Card initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
          <SectionTitle>🔧 数据源配置</SectionTitle>
          <FieldLabel>策略模式</FieldLabel>
          <Select value={dataSource.mode} onChange={e => setDataSource({ ...dataSource, mode: e.target.value as any })}>
            <option value="filter">选股策略</option>
            <option value="timing">择时策略</option>
            <option value="backtest">回测模式</option>
          </Select>
          <CheckboxRow>
            <label><input type="checkbox" checked={dataSource.sources.realtime} onChange={e => setDataSource({ ...dataSource, sources: { ...dataSource.sources, realtime: e.target.checked } })} /> 实时数据</label>
            <label><input type="checkbox" checked={dataSource.sources.historical} onChange={e => setDataSource({ ...dataSource, sources: { ...dataSource.sources, historical: e.target.checked } })} /> 历史数据</label>
          </CheckboxRow>
          <FieldLabel>股票池</FieldLabel>
          <ChipRow>
            {dataSource.stockPool.symbols.map(symbol => (
              <span key={symbol} style={{ padding: '4px 8px', background: '#3b82f6', color: 'white', borderRadius: '4px', fontSize: '0.8rem' }}>{symbol}</span>
            ))}
          </ChipRow>
        </Card>

        <Card initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
          <SectionTitle>🚀 优化控制</SectionTitle>
          <FieldLabel>目标函数</FieldLabel>
          <Select>
            <option>最大夏普比率</option>
            <option>最小回撤</option>
            <option>最大收益</option>
          </Select>
          <FieldLabel>进度 {optimization.progress.toFixed(1)}%</FieldLabel>
          <div style={{ height: '8px', background: '#e5e7eb', borderRadius: '4px', overflow: 'hidden', margin: '8px 0' }}>
            <motion.div
              style={{ height: '100%', background: 'linear-gradient(90deg, #3b82f6, #06b6d4)', borderRadius: '4px' }}
              initial={{ width: '0%' }}
              animate={{ width: `${optimization.progress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
          <div style={{ marginTop: 12 }}>
            <CyberButton 
              variant="primary" 
              simplified 
              onClick={handleStartOptimization}
              disabled={optimization.status === 'running'}
            >
              {optimization.status === 'running' ? '🔄 优化中...' : '🚀 开始优化'}
            </CyberButton>
          </div>
        </Card>

        <Card initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }}>
          <SectionTitle>📊 实时分析</SectionTitle>
          <KpiGrid>
            {metricsData.map(m => (
              <KpiCard key={m.label}>
                <div className="val">{m.value}</div>
                <div className="lbl">{m.label}</div>
              </KpiCard>
            ))}
          </KpiGrid>
          <div style={{ marginTop: 10 }}>
            <CyberButton variant="secondary" simplified>🔍 详细分析</CyberButton>
          </div>
        </Card>
      </Grid>
    </UnifiedMobileNav>
  );
}