import React, { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUIState } from '../../store/hooks';
import './BattleScene.scss';

// 战斗卡牌接口
interface BattleCard {
  id: string;
  name: string;
  type: 'filter' | 'timing' | 'backtest';
  icon: string;
  energy: number;
  power: number;
  description: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

// 战斗状态
interface BattleState {
  playerHealth: number;
  opponentHealth: number;
  playerEnergy: number;
  opponentEnergy: number;
  turn: 'player' | 'opponent';
  phase: 'preparation' | 'battle' | 'result';
  winner?: 'player' | 'opponent';
}

/**
 * 战斗场景 - 3D卡牌对战
 * 支持拖拽投放，实时战斗动画
 */
export default function BattleScene({ sceneData }: { sceneData?: any }) {
  const { switchScene } = useUIState();
  const battleFieldRef = useRef<HTMLDivElement>(null);
  
  const [battleState, setBattleState] = useState<BattleState>({
    playerHealth: 100,
    opponentHealth: 100,
    playerEnergy: 3,
    opponentEnergy: 3,
    turn: 'player',
    phase: 'preparation'
  });
  
  const [selectedCard, setSelectedCard] = useState<BattleCard | null>(null);
  const [playedCards, setPlayedCards] = useState<BattleCard[]>([]);
  
  // 玩家手牌
  const [playerHand] = useState<BattleCard[]>([
    {
      id: '1',
      name: 'MA均线策略',
      type: 'timing',
      icon: '📈',
      energy: 2,
      power: 15,
      description: '移动平均线交叉信号',
      rarity: 'common'
    },
    {
      id: '2', 
      name: 'RSI超买超卖',
      type: 'timing',
      icon: '🎯',
      energy: 2,
      power: 12,
      description: 'RSI指标判断买卖信号',
      rarity: 'common'
    },
    {
      id: '3',
      name: 'MACD金叉死叉',
      type: 'timing',
      icon: '🔗',
      energy: 3,
      power: 20,
      description: 'MACD指标信号',
      rarity: 'rare'
    },
    {
      id: '4',
      name: '市值筛选',
      type: 'filter',
      icon: '🔍',
      energy: 1,
      power: 8,
      description: '按市值筛选股票',
      rarity: 'common'
    },
    {
      id: '5',
      name: '神经网络预测',
      type: 'backtest',
      icon: '🧠',
      energy: 4,
      power: 30,
      description: '深度学习预测模型',
      rarity: 'legendary'
    }
  ]);

  useEffect(() => {
    console.log('Battle Scene initialized with data:', sceneData);
    
    // 开始战斗准备阶段
    setBattleState(prev => ({ ...prev, phase: 'preparation' }));
    
    // 3秒后进入战斗阶段
    const timer = setTimeout(() => {
      setBattleState(prev => ({ ...prev, phase: 'battle' }));
    }, 3000);
    
    return () => clearTimeout(timer);
  }, [sceneData]);

  /**
   * 卡牌拖拽开始
   */
  const handleDragStart = (card: BattleCard) => {
    setSelectedCard(card);
  };

  /**
   * 卡牌投放到战场
   */
  const handleCardPlay = (card: BattleCard) => {
    if (battleState.playerEnergy < card.energy) {
      console.log('Energy not enough!');
      return;
    }

    if (battleState.turn !== 'player') {
      console.log('Not your turn!');
      return;
    }

    // 扣除能量
    setBattleState(prev => ({
      ...prev,
      playerEnergy: prev.playerEnergy - card.energy
    }));

    // 添加到已出牌
    setPlayedCards(prev => [...prev, card]);
    
    // 执行卡牌效果
    executeCardEffect(card);
    
    setSelectedCard(null);
  };

  /**
   * 执行卡牌效果
   */
  const executeCardEffect = (card: BattleCard) => {
    // 模拟卡牌效果
    setTimeout(() => {
      setBattleState(prev => ({
        ...prev,
        opponentHealth: Math.max(0, prev.opponentHealth - card.power)
      }));
      
      // 检查胜利条件
      checkWinCondition();
    }, 1000);
  };

  /**
   * 检查胜利条件
   */
  const checkWinCondition = () => {
    if (battleState.opponentHealth <= 0) {
      setBattleState(prev => ({ 
        ...prev, 
        phase: 'result',
        winner: 'player'
      }));
    } else if (battleState.playerHealth <= 0) {
      setBattleState(prev => ({ 
        ...prev, 
        phase: 'result',
        winner: 'opponent'
      }));
    }
  };

  /**
   * 结束回合
   */
  const endTurn = () => {
    setBattleState(prev => ({
      ...prev,
      turn: prev.turn === 'player' ? 'opponent' : 'player',
      playerEnergy: prev.turn === 'player' ? 3 : prev.playerEnergy,
      opponentEnergy: prev.turn === 'opponent' ? 3 : prev.opponentEnergy
    }));
  };

  /**
   * 退出战斗
   */
  const exitBattle = () => {
    switchScene('Arena');
  };

  return (
    <div className="battle-scene">
      {/* 战斗背景 */}
      <div className="battle-background">
        <div className="bg-grid" />
        <div className="bg-energy-flow" />
        <div className="bg-particles" />
      </div>

      {/* 战斗界面 */}
      <div className="battle-interface">
        {/* 对手区域 */}
        <div className="opponent-area">
          <div className="opponent-info">
            <div className="opponent-avatar">🤖</div>
            <div className="opponent-details">
              <div className="opponent-name">AI Trader</div>
              <div className="health-bar">
                <div className="health-fill" style={{ width: `${battleState.opponentHealth}%` }} />
                <span className="health-text">{battleState.opponentHealth}/100</span>
              </div>
            </div>
            <div className="energy-display">
              <span className="energy-count">⚡{battleState.opponentEnergy}</span>
            </div>
          </div>
          
          {/* 对手卡牌区域 */}
          <div className="opponent-cards">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="card-back">
                <div className="card-back-pattern" />
              </div>
            ))}
          </div>
        </div>

        {/* 战场中央区域 */}
        <div className="battlefield" ref={battleFieldRef}>
          {/* 回合指示 */}
          <div className={`turn-indicator ${battleState.turn}`}>
            <div className="turn-arrow" />
            <span className="turn-text">
              {battleState.turn === 'player' ? 'Your Turn' : 'Opponent Turn'}
            </span>
          </div>
          
          {/* 战斗效果区域 */}
          <div className="battle-effects">
            <AnimatePresence>
              {playedCards.map((card, index) => (
                <motion.div
                  key={`${card.id}-${index}`}
                  className="battle-effect"
                  initial={{ scale: 0, rotateY: 0 }}
                  animate={{ 
                    scale: [0, 1.2, 1],
                    rotateY: [0, 180, 360]
                  }}
                  exit={{ scale: 0, opacity: 0 }}
                  transition={{ duration: 1.5 }}
                >
                  <div className="effect-icon">{card.icon}</div>
                  <div className="effect-power">-{card.power}</div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
          
          {/* 卡牌投放区域 */}
          <div 
            className="drop-zone"
            onDragOver={(e) => e.preventDefault()}
            onDrop={(e) => {
              e.preventDefault();
              if (selectedCard) {
                handleCardPlay(selectedCard);
              }
            }}
          >
            <div className="drop-zone-hint">
              {selectedCard ? `Drop ${selectedCard.name} here` : 'Drag cards here to play'}
            </div>
          </div>
        </div>

        {/* 玩家区域 */}
        <div className="player-area">
          <div className="player-info">
            <div className="player-avatar">👤</div>
            <div className="player-details">
              <div className="player-name">You</div>
              <div className="health-bar">
                <div className="health-fill" style={{ width: `${battleState.playerHealth}%` }} />
                <span className="health-text">{battleState.playerHealth}/100</span>
              </div>
            </div>
            <div className="energy-display">
              <span className="energy-count">⚡{battleState.playerEnergy}</span>
            </div>
          </div>
          
          {/* 玩家手牌 */}
          <div className="player-hand">
            {playerHand.map((card, index) => (
              <motion.div
                key={card.id}
                className={`battle-card ${card.rarity} ${selectedCard?.id === card.id ? 'selected' : ''}`}
                draggable
                onDragStart={() => handleDragStart(card)}
                onClick={() => setSelectedCard(card)}
                initial={{ y: 100, opacity: 0 }}
                animate={{ 
                  y: selectedCard?.id === card.id ? -20 : 0,
                  opacity: 1,
                  scale: selectedCard?.id === card.id ? 1.1 : 1
                }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -10, scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="card-energy">{card.energy}</div>
                <div className="card-icon">{card.icon}</div>
                <div className="card-name">{card.name}</div>
                <div className="card-power">⚔️ {card.power}</div>
                <div className="card-description">{card.description}</div>
                <div className={`card-rarity-indicator ${card.rarity}`} />
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* HUD元素 */}
      <div className="battle-hud">
        {/* 游戏状态 */}
        <div className="game-status">
          <div className="phase-indicator">{battleState.phase.toUpperCase()}</div>
          {battleState.phase === 'battle' && (
            <button className="end-turn-btn" onClick={endTurn}>
              End Turn
            </button>
          )}
        </div>
        
        {/* 控制按钮 */}
        <div className="control-buttons">
          <button className="control-btn" onClick={exitBattle}>
            退出
          </button>
        </div>
      </div>

      {/* 战斗结果弹窗 */}
      <AnimatePresence>
        {battleState.phase === 'result' && (
          <motion.div
            className="battle-result-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="result-panel"
              initial={{ scale: 0, rotateY: -180 }}
              animate={{ scale: 1, rotateY: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              <div className={`result-icon ${battleState.winner}`}>
                {battleState.winner === 'player' ? '🏆' : '💀'}
              </div>
              <h2 className="result-title">
                {battleState.winner === 'player' ? 'Victory!' : 'Defeat!'}
              </h2>
              <p className="result-message">
                {battleState.winner === 'player' 
                  ? 'Congratulations! You won the battle!' 
                  : 'Better luck next time!'}
              </p>
              <div className="result-actions">
                <button className="result-btn primary" onClick={() => switchScene('Arena')}>
                  Return to Arena
                </button>
                <button className="result-btn secondary" onClick={() => window.location.reload()}>
                  Battle Again
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
