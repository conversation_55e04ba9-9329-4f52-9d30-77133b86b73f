@use "../styles/themes/cyber-theme" as cyber;

.game-shell {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  
  // 禁用用户交互行为
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  
  // 移动端适配
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  
  // 禁用滚动和缩放
  touch-action: none;
  overscroll-behavior: none;
  
  // 层级结构
  z-index: 9999;
  
  // 背景层
  &__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -3;
    
    .background-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 40px 40px;
      animation: grid-pulse 4s ease-in-out infinite alternate;
      z-index: -3;
      
      @keyframes grid-pulse {
        0% { opacity: 0.3; }
        100% { opacity: 0.1; }
      }
    }
    
    .background-particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 255, 0.5), transparent),
                  radial-gradient(2px 2px at 40px 70px, rgba(255, 0, 255, 0.4), transparent),
                  radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 0, 0.6), transparent),
                  radial-gradient(1px 1px at 130px 80px, rgba(0, 255, 255, 0.4), transparent),
                  radial-gradient(2px 2px at 160px 30px, rgba(255, 0, 255, 0.3), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: particles-float 20s linear infinite;
      z-index: -2;
      
      @keyframes particles-float {
        0% { transform: translate(0px, 0px); }
        33% { transform: translate(30px, -30px); }
        66% { transform: translate(-20px, 20px); }
        100% { transform: translate(0px, 0px); }
      }
    }
    
    .background-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 20, 40, 0.8) 25%,
        rgba(20, 0, 40, 0.8) 50%,
        rgba(0, 40, 20, 0.8) 75%,
        rgba(0, 0, 0, 0.9) 100%
      );
      z-index: -1;
    }
  }
  
  // 内容层
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    
    .game-shell__scene {
      width: 100%;
      height: 100%;
    }
  }
  
  // HUD层
  &__hud {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    pointer-events: none;
    
    // HUD子元素可以选择性启用指针事件
    * {
      pointer-events: auto;
    }
  }
  
  // 游戏控制按钮
  &__controls {
    position: absolute;
    top: env(safe-area-inset-top, 20px);
    right: env(safe-area-inset-right, 20px);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1001;
    
    .control-btn {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      border: 2px solid rgba(0, 255, 255, 0.6);
      background: rgba(0, 0, 0, 0.7);
      color: #00ffff;
      font-size: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      
      &:hover {
        border-color: #00ffff;
        background: rgba(0, 255, 255, 0.1);
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      }
      
      &:active {
        transform: scale(0.95);
      }
      
      &--settings {
        // 设置按钮特殊样式
      }
      
      &--fullscreen {
        // 全屏按钮特殊样式
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .game-shell {
    &__controls {
      top: 10px;
      right: 10px;
      
      .control-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }
    }
    
    &__background {
      .background-grid {
        background-size: 20px 20px;
      }
      
      .background-particles {
        background-size: 100px 50px;
      }
    }
  }
}

// 横屏模式适配
@media (orientation: landscape) and (max-height: 500px) {
  .game-shell {
    &__controls {
      top: 5px;
      right: 5px;
      
      .control-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
      }
    }
  }
}

// 高性能模式（移动端性能优化）
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .game-shell {
    &__background {
      .background-particles {
        display: none; // 在高DPI移动设备上隐藏粒子以提升性能
      }
      
      .background-grid {
        animation: none; // 禁用动画以节省电量
      }
    }
  }
}