"""
简化的用户卡牌服务
替代原有的复杂库存系统
"""
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from ..models.user_cards import UserCards, UserCardLog
from ..models.user import User
from ..models.strategy_template import StrategyTemplate
from ..utils.response_utils import ResponseFormatter
from bson import ObjectId

logger = logging.getLogger(__name__)

class UserCardsService:
    """简化的用户卡牌服务"""
    
    @staticmethod
    async def get_user_cards(user_id: str) -> Dict[str, Any]:
        """获取用户卡牌库存"""
        try:
            # 检查是否为guest用户
            if user_id.startswith('guest_'):
                return ResponseFormatter.error("访客用户无法访问库存功能", code=403)
            
            # 验证用户ID格式
            try:
                ObjectId(user_id)
            except Exception:
                return ResponseFormatter.error("无效的用户ID格式", code=400)
            
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
            
            # 获取用户卡牌
            user_cards = await UserCards.get_by_user_id(user_id)
            if not user_cards:
                # 用户没有卡牌，创建空库存
                user_cards = UserCards(user_id=user_id)
                await user_cards.save()
            
            # 转换为前端期望的格式
            inventory_items = user_cards.to_inventory_format()
            
            summary = {
                "total_cards": user_cards.total_cards,
                "total_types": len(user_cards.cards),
                "available_types": len([k for k, v in user_cards.cards.items() if v > 0]),
                "items": inventory_items,
                "stats": {
                    "total_drawn": user_cards.total_drawn,
                    "total_used": user_cards.total_used,
                    "last_draw_time": user_cards.last_draw_time.isoformat() if user_cards.last_draw_time else None
                }
            }
            
            return ResponseFormatter.success(summary, "获取库存成功")
            
        except Exception as e:
            logger.error(f"获取用户库存失败: {e}")
            return ResponseFormatter.error(f"获取库存失败: {str(e)}")

    @staticmethod
    async def add_cards(user_id: str, template_id: str, quantity: int, source: str = "draw") -> Dict[str, Any]:
        """添加卡牌到用户库存"""
        try:
            # 基本验证
            if user_id.startswith('guest_'):
                return ResponseFormatter.error("访客用户无法获得卡牌", code=403)
            
            try:
                ObjectId(user_id)
            except Exception:
                return ResponseFormatter.error("无效的用户ID格式", code=400)
            
            # 获取或创建用户卡牌记录
            user_cards = await UserCards.get_by_user_id(user_id)
            if not user_cards:
                user_cards = UserCards(user_id=user_id)
            
            # 添加卡牌
            await user_cards.add_cards(template_id, quantity)
            
            # 记录日志
            await UserCardLog.log_operation(
                user_id=user_id,
                operation="add",
                changes={template_id: quantity},
                source=source,
                metadata={"timestamp": datetime.utcnow().isoformat()}
            )
            
            return ResponseFormatter.success({
                "template_id": template_id,
                "quantity": quantity,
                "new_total": user_cards.get_card_quantity(template_id)
            }, "卡牌添加成功")
            
        except Exception as e:
            logger.error(f"添加卡牌失败: {e}")
            return ResponseFormatter.error(f"添加卡牌失败: {str(e)}")

    @staticmethod
    async def use_cards(user_id: str, template_id: str, quantity: int, reason: str = "strategy_execution") -> Dict[str, Any]:
        """使用卡牌"""
        try:
            # 基本验证
            if user_id.startswith('guest_'):
                return ResponseFormatter.error("访客用户无法使用卡牌", code=403)
            
            # 获取用户卡牌记录
            user_cards = await UserCards.get_by_user_id(user_id)
            if not user_cards:
                return ResponseFormatter.error("用户没有任何卡牌", code=400)
            
            # 检查卡牌数量
            if not user_cards.has_cards(template_id, quantity):
                current_qty = user_cards.get_card_quantity(template_id)
                return ResponseFormatter.error(f"卡牌数量不足，当前拥有: {current_qty}, 需要: {quantity}", code=400)
            
            # 使用卡牌
            success = await user_cards.use_cards(template_id, quantity)
            if not success:
                return ResponseFormatter.error("使用卡牌失败", code=500)
            
            # 记录日志
            await UserCardLog.log_operation(
                user_id=user_id,
                operation="use",
                changes={template_id: -quantity},
                source="strategy_execution",
                metadata={"reason": reason, "timestamp": datetime.utcnow().isoformat()}
            )
            
            return ResponseFormatter.success({
                "template_id": template_id,
                "used_quantity": quantity,
                "remaining": user_cards.get_card_quantity(template_id)
            }, "卡牌使用成功")
            
        except Exception as e:
            logger.error(f"使用卡牌失败: {e}")
            return ResponseFormatter.error(f"使用卡牌失败: {str(e)}")

    @staticmethod
    async def check_card_availability(user_id: str, template_id: str, required_quantity: int = 1) -> Dict[str, Any]:
        """检查卡牌可用性"""
        try:
            if user_id.startswith('guest_'):
                return ResponseFormatter.error("访客用户无卡牌", code=403)
            
            user_cards = await UserCards.get_by_user_id(user_id)
            if not user_cards:
                return ResponseFormatter.success({
                    "available": False,
                    "current_quantity": 0,
                    "required_quantity": required_quantity
                }, "用户无任何卡牌")
            
            current_qty = user_cards.get_card_quantity(template_id)
            available = current_qty >= required_quantity
            
            return ResponseFormatter.success({
                "available": available,
                "current_quantity": current_qty,
                "required_quantity": required_quantity
            }, "检查完成")
            
        except Exception as e:
            logger.error(f"检查卡牌可用性失败: {e}")
            return ResponseFormatter.error(f"检查失败: {str(e)}")

    @staticmethod
    async def draw_cards(user_id: str, count: int = 1, pack_type: str = "standard") -> Dict[str, Any]:
        """抽卡功能"""
        try:
            if user_id.startswith('guest_'):
                return ResponseFormatter.error("访客用户无法抽卡", code=403)
            
            # 获取可抽取的策略模板
            collection = await StrategyTemplate.get_collection()
            templates = []
            async for doc in collection.find({"is_active": True}):
                doc['id'] = str(doc.pop('_id'))
                templates.append(StrategyTemplate(**doc))
            
            if not templates:
                return ResponseFormatter.error("暂无可抽取的卡牌", code=400)
            
            # 抽卡概率配置
            rarity_weights = {
                "common": 60,
                "rare": 25,
                "epic": 12,
                "legendary": 2.8,
                "mythic": 0.2
            }
            
            drawn_cards = []
            changes = {}
            
            for _ in range(min(count, 10)):  # 限制最多10张
                # 按权重随机选择稀有度
                import random
                rarity_pool = []
                for rarity, weight in rarity_weights.items():
                    rarity_pool.extend([rarity] * int(weight * 10))
                
                selected_rarity = random.choice(rarity_pool)
                
                # 从对应稀有度中随机选择卡牌
                rarity_templates = []
                for template in templates:
                    template_rarity = "common"
                    if "rare" in template.tags:
                        template_rarity = "rare"
                    elif "epic" in template.tags:
                        template_rarity = "epic"
                    elif "legendary" in template.tags:
                        template_rarity = "legendary"
                    elif "mythic" in template.tags:
                        template_rarity = "mythic"
                    
                    if template_rarity == selected_rarity:
                        rarity_templates.append(template)
                
                # 如果该稀有度没有卡牌，降级到普通卡牌
                if not rarity_templates:
                    rarity_templates = [t for t in templates if "common" in t.tags or not any(r in t.tags for r in ["rare", "epic", "legendary", "mythic"])]
                
                if rarity_templates:
                    selected_template = random.choice(rarity_templates)
                    template_id = selected_template.template_id
                    
                    # 统计抽到的卡牌
                    changes[template_id] = changes.get(template_id, 0) + 1
                    
                    drawn_cards.append({
                        "template_id": template_id,
                        "name": selected_template.name,
                        "description": selected_template.description,
                        "rarity": selected_rarity,
                        "icon": selected_template.ui.icon if selected_template.ui and selected_template.ui.icon else "📊",
                        "color": selected_template.ui.color if selected_template.ui and selected_template.ui.color else "#1890ff",
                        "is_new": True
                    })
            
            # 批量添加到用户库存
            user_cards = await UserCards.get_by_user_id(user_id)
            if not user_cards:
                user_cards = UserCards(user_id=user_id)
            
            for template_id, qty in changes.items():
                await user_cards.add_cards(template_id, qty)
            
            # 记录抽卡日志
            await UserCardLog.log_operation(
                user_id=user_id,
                operation="draw",
                changes=changes,
                source="card_draw",
                metadata={"pack_type": pack_type, "count": count}
            )
            
            return ResponseFormatter.success({
                "drawn_cards": drawn_cards,
                "draw_count": len(drawn_cards),
                "changes": changes
            }, f"成功抽取 {len(drawn_cards)} 张卡牌！")
            
        except Exception as e:
            logger.error(f"抽卡失败: {e}")
            return ResponseFormatter.error(f"抽卡失败: {str(e)}") 