/**
 * 🏗️ 策略切片 - 管理策略组和策略卡牌实例
 * 支持策略组创建、删除、更新、卡牌管理和性能跟踪
 * 使用原生immutable更新模式，保持Map数据结构的高性能
 */

import type { SliceCreator, StrategySlice } from '../types/store'
import type { StrategyGroup, StrategyCardInstance } from '../../types/game'

// 🎯 创建策略切片
export const createStrategySlice: SliceCreator<StrategySlice> = (set, get) => ({
  // 🏗️ 初始状态
  groups: [],
  currentGroupId: null,
  loading: false,
  error: undefined,

  // 📂 加载策略组
  loadGroups: async () => {
    const currentState = get()
    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 模拟 API 调用
      const mockGroups: StrategyGroup[] = [
        {
          id: 'group_1',
          name: '趋势跟踪策略组',
          description: '基于技术指标的趋势跟踪策略',
          cards: [],
          execution_mode: 'sequential',
          status: 'active',
          performance: {
            total_return: 15.6,
            win_rate: 68.5,
            max_drawdown: -8.2,
            sharpe_ratio: 1.45
          },
          created_at: new Date('2024-01-10'),
          updated_at: new Date('2024-01-15')
        },
        {
          id: 'group_2',
          name: '均值回归策略组',
          description: '基于均值回归原理的短期交易策略',
          cards: [],
          execution_mode: 'parallel',
          status: 'testing',
          performance: {
            total_return: 8.3,
            win_rate: 61.2,
            max_drawdown: -6.5,
            sharpe_ratio: 1.12
          },
          created_at: new Date('2024-02-01'),
          updated_at: new Date('2024-02-08')
        }
      ]

      set({
        ...get(),
        groups: mockGroups,
        loading: false,
        error: undefined
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '策略组加载失败'
      set({
        ...get(),
        loading: false,
        error: errorMessage
      })
    }
  },

  // 🆕 创建策略组
  createGroup: async (name: string, description: string): Promise<string> => {
    try {
      const groupId = `group_${Date.now()}`
      const newGroup: StrategyGroup = {
        id: groupId,
        name,
        description,
        cards: [],
        execution_mode: 'sequential',
        status: 'inactive',
        performance: {
          total_return: 0,
          win_rate: 0,
          max_drawdown: 0,
          sharpe_ratio: 0
        },
        created_at: new Date(),
        updated_at: new Date()
      }

      const currentState = get()
      const newGroups = [...currentState.groups, newGroup]

      set({
        ...currentState,
        groups: newGroups
      })

      return groupId
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建策略组失败'
      set({
        ...get(),
        error: errorMessage
      })
      throw new Error(errorMessage)
    }
  },

  // 🗑️ 删除策略组
  deleteGroup: async (groupId: string) => {
    try {
      const currentState = get()
      const newGroups = currentState.groups.filter(group => group.id !== groupId)

      set({
        ...currentState,
        groups: newGroups,
        currentGroupId: currentState.currentGroupId === groupId ? null : currentState.currentGroupId
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除策略组失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🎯 设置当前策略组
  setCurrentGroup: (groupId: string | null) => {
    const currentState = get()
    set({
      ...currentState,
      currentGroupId: groupId
    })
  },

  // 🂠 添加卡牌到策略组
  addCardToGroup: async (groupId: string, cardInstance: StrategyCardInstance) => {
    try {
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)
      
      if (groupIndex === -1) {
        throw new Error('策略组不存在')
      }

      const newGroups = [...currentState.groups]
      newGroups[groupIndex] = {
        ...newGroups[groupIndex],
        cards: [...newGroups[groupIndex].cards, cardInstance],
        updated_at: new Date()
      }

      set({
        ...currentState,
        groups: newGroups
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '添加卡牌失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🗑️ 从策略组移除卡牌
  removeCardFromGroup: async (groupId: string, cardId: string) => {
    try {
      const currentState = get()
      const groupIndex = currentState.groups.findIndex(g => g.id === groupId)
      
      if (groupIndex === -1) {
        throw new Error('策略组不存在')
      }

      const newGroups = [...currentState.groups]
      newGroups[groupIndex] = {
        ...newGroups[groupIndex],
        cards: newGroups[groupIndex].cards.filter(card => card.id !== cardId),
        updated_at: new Date()
      }

      set({
        ...currentState,
        groups: newGroups
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '移除卡牌失败'
      set({
        ...get(),
        error: errorMessage
      })
    }
  },

  // 🔧 错误处理
  clearError: () => {
    set({
      ...get(),
      error: undefined
    })
  },

  // 🔄 重置策略状态
  resetStrategyState: () => {
    set({
      groups: [],
      currentGroupId: null,
      loading: false,
      error: undefined
    })
  }
})
