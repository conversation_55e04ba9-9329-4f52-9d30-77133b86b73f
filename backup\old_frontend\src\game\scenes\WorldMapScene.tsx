import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useSceneNavigation } from '../SceneRouter';
import './WorldMapScene.scss';

interface WorldMapPoint {
  id: string;
  name: string;
  type: 'arena' | 'collection' | 'shop' | 'strategy' | 'profile';
  position: { x: number; y: number };
  icon: string;
  unlocked: boolean;
  description: string;
}

/**
 * 世界地图场景 - 游戏主页面
 * 3D互动地图，提供各功能区域的入口
 */
export default function WorldMapScene({ sceneData }: { sceneData?: any }) {
  const navigation = useSceneNavigation();
  const mapRef = useRef<HTMLDivElement>(null);
  const [selectedPoint, setSelectedPoint] = useState<WorldMapPoint | null>(null);
  const [mapPoints] = useState<WorldMapPoint[]>([
    {
      id: 'arena',
      name: '竞技场',
      type: 'arena',
      position: { x: 30, y: 40 },
      icon: '⚔️',
      unlocked: true,
      description: '与其他玩家进行策略对战'
    },
    {
      id: 'collection',
      name: '卡牌收藏',
      type: 'collection',
      position: { x: 70, y: 25 },
      icon: '🃏',
      unlocked: true,
      description: '查看和管理你的策略卡牌'
    },
    {
      id: 'shop',
      name: '商店',
      type: 'shop',
      position: { x: 20, y: 70 },
      icon: '🛒',
      unlocked: true,
      description: '购买新的策略卡牌和道具'
    },
    {
      id: 'strategy',
      name: '策略工坊',
      type: 'strategy',
      position: { x: 80, y: 60 },
      icon: '📋',
      unlocked: true,
      description: '创建和编辑交易策略'
    },
    {
      id: 'profile',
      name: '个人中心',
      type: 'profile',
      position: { x: 50, y: 80 },
      icon: '👤',
      unlocked: true,
      description: '查看个人资料和成就'
    }
  ]);

  useEffect(() => {
    // 地图初始化逻辑
    console.log('WorldMap Scene initialized with data:', sceneData);
  }, [sceneData]);

  /**
   * 处理地图点击
   */
  const handlePointClick = (point: WorldMapPoint) => {
    if (!point.unlocked) {
      return;
    }

    setSelectedPoint(point);

    // 延迟导航，让选中动画播放
    setTimeout(() => {
      switch (point.type) {
        case 'arena':
          navigation.goToArena();
          break;
        case 'collection':
          navigation.goToCollection();
          break;
        case 'shop':
          navigation.goToShop();
          break;
        case 'strategy':
          navigation.goToStrategy();
          break;
        case 'profile':
          navigation.goToProfile();
          break;
      }
    }, 800);
  };

  /**
   * 处理背景点击（取消选中）
   */
  const handleBackgroundClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setSelectedPoint(null);
    }
  };

  return (
    <div className="world-map-scene" onClick={handleBackgroundClick}>
      {/* 3D地图容器 */}
      <div className="map-container" ref={mapRef}>
        {/* 地图背景层 */}
        <div className="map-background">
          {/* 动态网格背景 */}
          <div className="background-grid" />
          
          {/* 地形轮廓 */}
          <div className="terrain-layer">
            <svg className="terrain-svg" viewBox="0 0 1000 600">
              {/* 山脉轮廓 */}
              <path
                d="M0,400 Q200,200 400,300 T800,250 L1000,300 L1000,600 L0,600 Z"
                fill="url(#mountainGradient)"
                opacity="0.3"
              />
              {/* 河流路径 */}
              <path
                d="M0,450 Q300,400 500,420 T1000,380"
                stroke="rgba(0,191,255,0.4)"
                strokeWidth="8"
                fill="none"
              />
              
              <defs>
                <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="rgba(100,100,100,0.8)" />
                  <stop offset="100%" stopColor="rgba(50,50,50,0.4)" />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>

        {/* 地图交互点 */}
        <div className="map-points">
          {mapPoints.map((point) => (
            <motion.div
              key={point.id}
              className={`map-point ${point.unlocked ? 'unlocked' : 'locked'} ${
                selectedPoint?.id === point.id ? 'selected' : ''
              }`}
              style={{
                left: `${point.position.x}%`,
                top: `${point.position.y}%`,
              }}
              onClick={() => handlePointClick(point)}
              whileHover={{ scale: point.unlocked ? 1.2 : 1 }}
              whileTap={{ scale: point.unlocked ? 0.9 : 1 }}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ 
                opacity: 1, 
                scale: 1,
                rotate: selectedPoint?.id === point.id ? 360 : 0
              }}
              transition={{ 
                duration: 0.5, 
                delay: mapPoints.indexOf(point) * 0.1,
                rotate: { duration: 1 }
              }}
            >
              {/* 光环效果 */}
              <div className="point-aura" />
              
              {/* 图标 */}
              <div className="point-icon">
                {point.icon}
              </div>
              
              {/* 脉冲效果 */}
              <div className="point-pulse" />
              
              {/* 标题 */}
              <div className="point-label">
                {point.name}
              </div>
              
              {/* 连接线（到中心或其他点） */}
              {point.unlocked && (
                <div className="point-connection" />
              )}
            </motion.div>
          ))}
        </div>

        {/* 中央logo或标题 */}
        <div className="map-center">
          <motion.div
            className="center-logo"
            initial={{ opacity: 0, rotateY: -180 }}
            animate={{ opacity: 1, rotateY: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
          >
            <div className="logo-ring">
              <div className="logo-inner">
                <span className="logo-text">QuantCard</span>
                <span className="logo-subtitle">Arena</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 信息面板 */}
      {selectedPoint && (
        <motion.div
          className="info-panel"
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          transition={{ duration: 0.3 }}
        >
          <div className="panel-header">
            <div className="panel-icon">{selectedPoint.icon}</div>
            <h3 className="panel-title">{selectedPoint.name}</h3>
          </div>
          <p className="panel-description">{selectedPoint.description}</p>
          <div className="panel-actions">
            <button 
              className="enter-btn"
              onClick={() => handlePointClick(selectedPoint)}
            >
              进入 →
            </button>
          </div>
        </motion.div>
      )}

      {/* HUD元素 */}
      <div className="world-map-hud">
        {/* 玩家信息 */}
        <div className="player-info">
          <div className="player-avatar">👤</div>
          <div className="player-details">
            <div className="player-name">Player</div>
            <div className="player-level">Lv. 1</div>
          </div>
        </div>

        {/* 快捷操作 */}
        <div className="quick-actions">
          <button 
            className="quick-btn"
            onClick={() => navigation.goToSettings()}
            title="设置"
          >
            ⚙️
          </button>
        </div>
      </div>

      {/* 浮动粒子效果 */}
      <div className="floating-particles">
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${5 + Math.random() * 10}s`
            }}
          />
        ))}
      </div>
    </div>
  );
}