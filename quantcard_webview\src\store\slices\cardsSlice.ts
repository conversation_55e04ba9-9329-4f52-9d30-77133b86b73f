/**
 * 🎒 卡牌库存切片 - 管理用户卡牌收藏和库存操作
 * 支持完整的库存管理、搜索、数量跟踪和错误处理
 * 使用原生immutable更新模式，保持Map数据结构的高性能
 */

import type { StateCreator } from 'zustand'
import type { CardsSlice } from '../types/store'
import type { InventoryItem } from '../../types/game'
import { unifiedWebSocket, MessageType } from '../../services/unifiedWebSocket'

// 🎯 创建卡牌切片
export const createCardsSlice: StateCreator<
  CardsSlice,
  [['zustand/subscribeWithSelector', never]],
  [],
  CardsSlice
> = (set, get) => ({
  // 🎒 初始状态
  inventory: [],
  searchIndex: new Map(),
  loading: false,
  error: undefined,

  // 💼 库存加载
  loadInventory: async () => {
    const currentState = get()
    
    // 检查认证状态
    const authState = window?.localStorage?.getItem('quantcard_auth') || window?.sessionStorage?.getItem('quantcard_auth')
    if (!authState) {
      console.log('用户未登录，跳过库存加载')
      set({
        ...currentState,
        inventory: [],
        searchIndex: new Map(),
        loading: false,
        error: '请先登录'
      })
      return
    }

    const auth = JSON.parse(authState)
    if (!auth.user || auth.user.id?.startsWith('guest_')) {
      console.log('访客用户，跳过库存加载')
      set({
        ...currentState,
        inventory: [],
        searchIndex: new Map(),
        loading: false,
        error: '访客用户无法查看库存'
      })
      return
    }

    set({
      ...currentState,
      loading: true,
      error: undefined
    })

    try {
      // 通过WebSocket获取真实库存数据
      const result = await unifiedWebSocket.getInventory(false)
      
      if (result && result.success) {
        const inventoryData = result.data?.items || []
        const currentState = get()

        // 保存现有的临时使用状态
        const existingTempUsed = new Map<string, number>()
        currentState.inventory.forEach(item => {
          if (item.temp_used && item.temp_used > 0) {
            existingTempUsed.set(item.template_id, item.temp_used)
          }
        })

        // 转换数据格式，保留临时使用状态
        const inventory: InventoryItem[] = inventoryData.map((item: any) => ({
          template_id: item.template_id,
          quantity: item.quantity || 0,
          acquired_at: new Date(item.acquired_at || item.updated_at),
          total_acquired: item.total_acquired || item.quantity || 0,
          source: (item.source || 'activity') as InventoryItem['source'],
          // 保留现有的临时使用状态
          temp_used: existingTempUsed.get(item.template_id) || 0
        }))

        // 构建搜索索引
        const newSearchIndex = new Map<string, Set<string>>()
        inventory.forEach(item => {
          const searchTerms = new Set([
            item.template_id.toLowerCase(),
            ...item.template_id.split('_'),
            ...item.template_id.split('-')
          ])
          newSearchIndex.set(item.template_id, searchTerms)
        })

        set({
          ...currentState,
          inventory,
          searchIndex: newSearchIndex,
          loading: false,
          error: undefined
        })
      } else {
        // 库存为空或获取失败
        const errorMsg = result?.message || '库存获取失败'
        console.log('库存获取结果:', errorMsg)
        
        set({
          ...get(),
          inventory: [],
          searchIndex: new Map(),
          loading: false,
          error: errorMsg.includes('访客') ? errorMsg : undefined
        })
      }
    } catch (error) {
      console.error('加载库存失败:', error)
      const errorMessage = error instanceof Error ? error.message : '网络错误'
      
      set({
        ...get(),
        inventory: [],
        searchIndex: new Map(),
        loading: false,
        error: errorMessage.includes('访客') ? errorMessage : '加载库存失败'
      })
    }
  },

  // 🎁 添加卡牌
  addCards: async (templateId: string, quantity: number, source: string = 'activity') => {
    try {
      const result = await unifiedWebSocket.addCard(templateId, quantity, source)
      
      if (result && result.success) {
        // 更新本地状态
        const currentState = get()
        const existingIndex = currentState.inventory.findIndex(item => item.template_id === templateId)
        
        if (existingIndex >= 0) {
          // 更新现有库存
          const updatedInventory = [...currentState.inventory]
          updatedInventory[existingIndex] = {
            ...updatedInventory[existingIndex],
            quantity: result.data.quantity,
            total_acquired: result.data.total_acquired
          }
          
          set({
            ...currentState,
            inventory: updatedInventory
          })
        } else {
          // 添加新库存项
          const newItem: InventoryItem = {
            template_id: templateId,
            quantity: quantity,
            acquired_at: new Date(),
            total_acquired: quantity,
            source: source as InventoryItem['source']
          }
          
          set({
            ...currentState,
            inventory: [...currentState.inventory, newItem]
          })
        }
        
        console.log(`成功添加 ${quantity} 个 ${templateId}`)
      } else {
        throw new Error(result?.message || '添加卡牌失败')
      }
    } catch (error) {
      console.error('添加卡牌失败:', error)
      set({
        ...get(),
        error: error instanceof Error ? error.message : '添加卡牌失败'
      })
      throw error
    }
  },

  // 🔥 消耗卡牌（真正的消费，用于保存策略组时）
  consumeCard: async (templateId: string, quantity: number = 1): Promise<boolean> => {
    try {
      // 先检查库存是否充足
      const currentState = get()
      const availableQuantity = currentState.getAvailableQuantity(templateId)

      if (availableQuantity < quantity) {
        set({
          ...currentState,
          error: `库存不足，当前数量: ${availableQuantity}，需要: ${quantity}`
        })
        return false
      }

      const result = await unifiedWebSocket.consumeCard(templateId, quantity, 'strategy_save')

      if (result && result.success) {
        // 更新本地状态
        const existingIndex = currentState.inventory.findIndex(item => item.template_id === templateId)

        if (existingIndex >= 0) {
          const updatedInventory = [...currentState.inventory]
          updatedInventory[existingIndex] = {
            ...updatedInventory[existingIndex],
            quantity: Math.max(0, updatedInventory[existingIndex].quantity - quantity)
          }

          set({
            ...currentState,
            inventory: updatedInventory,
            error: undefined
          })
        }

        console.log(`成功消耗 ${quantity} 个 ${templateId}`)
        return true
      } else {
        throw new Error(result?.message || '消耗卡牌失败')
      }
    } catch (error) {
      console.error('消耗卡牌失败:', error)
      set({
        ...get(),
        error: error instanceof Error ? error.message : '消耗卡牌失败'
      })
      return false
    }
  },

  // 🎯 临时使用卡牌（用于构建区，不真正消费）
  tempUseCard: (templateId: string, quantity: number = 1): boolean => {
    const currentState = get()
    const availableQuantity = currentState.getAvailableQuantity(templateId)

    if (availableQuantity < quantity) {
      set({
        ...currentState,
        error: `库存不足，当前数量: ${availableQuantity}，需要: ${quantity}`
      })
      return false
    }

    // 更新临时使用状态
    const existingIndex = currentState.inventory.findIndex(item => item.template_id === templateId)

    if (existingIndex >= 0) {
      const updatedInventory = [...currentState.inventory]
      updatedInventory[existingIndex] = {
        ...updatedInventory[existingIndex],
        temp_used: (updatedInventory[existingIndex].temp_used || 0) + quantity
      }

      set({
        ...currentState,
        inventory: updatedInventory,
        error: undefined
      })
    }

    console.log(`临时使用 ${quantity} 个 ${templateId}`)
    return true
  },

  // 🔄 释放临时使用的卡牌
  releaseTempCard: (templateId: string, quantity: number = 1): void => {
    const currentState = get()
    const existingIndex = currentState.inventory.findIndex(item => item.template_id === templateId)

    if (existingIndex >= 0) {
      const updatedInventory = [...currentState.inventory]
      const currentTempUsed = updatedInventory[existingIndex].temp_used || 0
      updatedInventory[existingIndex] = {
        ...updatedInventory[existingIndex],
        temp_used: Math.max(0, currentTempUsed - quantity)
      }

      set({
        ...currentState,
        inventory: updatedInventory
      })
    }

    console.log(`释放临时使用 ${quantity} 个 ${templateId}`)
  },

  // 📊 获取可用数量（扣除临时使用的）
  getAvailableQuantity: (templateId: string): number => {
    const currentState = get()
    const item = currentState.inventory.find(item => item.template_id === templateId)
    if (!item) return 0
    return Math.max(0, item.quantity - (item.temp_used || 0))
  },

  // 📊 获取总数量（不扣除临时使用的）
  getTotalQuantity: (templateId: string): number => {
    const currentState = get()
    const item = currentState.inventory.find(item => item.template_id === templateId)
    return item ? item.quantity : 0
  },

  // 🔍 搜索卡牌
  searchCards: (query: string): InventoryItem[] => {
    if (!query.trim()) {
      return get().inventory
    }

    const currentState = get()
    const lowerQuery = query.toLowerCase()
    
    return currentState.inventory.filter(item => {
      const searchTerms = currentState.searchIndex.get(item.template_id)
      if (!searchTerms) return false
      
      return Array.from(searchTerms).some(term => 
        (term as string).includes(lowerQuery) || lowerQuery.includes(term as string)
      )
    })
  },

  // 🧹 清理错误状态
  clearError: () => {
    set({
      ...get(),
      error: undefined
    })
  },

  // 🔄 重置状态
  resetCardsState: () => {
    set({
      ...get(),
      inventory: [],
      searchIndex: new Map(),
      loading: false,
      error: undefined
    })
  }
})
