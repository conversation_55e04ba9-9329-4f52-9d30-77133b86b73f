@use "../../styles/themes/cyber-theme" as cyber;

.battle-scene {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #001122 0%, #002244 50%, #000811 100%);
}

// 战斗背景
.battle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  
  .bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgba(0, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: grid-pulse 3s ease-in-out infinite alternate;
  }
  
  .bg-energy-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(0, 100, 255, 0.1) 0%, transparent 70%);
    animation: energy-flow 4s ease-in-out infinite;
  }
  
  .bg-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(1px 1px at 20% 30%, rgba(255, 255, 255, 0.3), transparent),
      radial-gradient(1px 1px at 80% 70%, rgba(0, 255, 255, 0.3), transparent),
      radial-gradient(1px 1px at 40% 80%, rgba(255, 0, 255, 0.3), transparent);
    background-size: 200px 200px;
    animation: particles-drift 15s linear infinite;
  }
}

// 战斗界面
.battle-interface {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  z-index: 1;
  
  // 对手区域
  .opponent-area {
    flex: 0 0 25%;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
    
    .opponent-info {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .opponent-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #ff4757, #ff6b9d);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        border: 2px solid rgba(255, 71, 87, 0.5);
      }
      
      .opponent-details {
        .opponent-name {
          color: #ff6b9d;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .health-bar {
          position: relative;
          width: 200px;
          height: 20px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 10px;
          overflow: hidden;
          border: 1px solid rgba(255, 71, 87, 0.3);
          
          .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4757, #ff6b9d);
            transition: width 0.5s ease;
          }
          
          .health-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
          }
        }
      }
      
      .energy-display {
        .energy-count {
          background: rgba(255, 255, 0, 0.2);
          color: #ffff00;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 600;
          border: 1px solid rgba(255, 255, 0, 0.5);
        }
      }
    }
    
    .opponent-cards {
      display: flex;
      gap: 10px;
      
      .card-back {
        width: 80px;
        height: 110px;
        background: linear-gradient(145deg, #2c3e50, #3498db);
        border-radius: 8px;
        border: 2px solid rgba(52, 152, 219, 0.5);
        position: relative;
        overflow: hidden;
        
        .card-back-pattern {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: repeating-linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.1) 0px,
            rgba(255, 255, 255, 0.1) 2px,
            transparent 2px,
            transparent 10px
          );
          animation: card-shimmer 2s linear infinite;
        }
      }
    }
  }
  
  // 战场中央区域
  .battlefield {
    flex: 1;
    position: relative;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .turn-indicator {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 20px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      
      &.player {
        background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(0, 150, 255, 0.3));
        color: #00ffff;
        border: 1px solid rgba(0, 255, 255, 0.5);
        
        .turn-arrow {
          border-left: 8px solid #00ffff;
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }
      }
      
      &.opponent {
        background: linear-gradient(45deg, rgba(255, 71, 87, 0.2), rgba(255, 107, 157, 0.3));
        color: #ff6b9d;
        border: 1px solid rgba(255, 71, 87, 0.5);
        
        .turn-arrow {
          border-right: 8px solid #ff6b9d;
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }
      }
    }
    
    .battle-effects {
      position: relative;
      width: 100%;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .battle-effect {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        
        .effect-icon {
          font-size: 60px;
          filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.8));
        }
        
        .effect-power {
          color: #ff4757;
          font-size: 24px;
          font-weight: 700;
          text-shadow: 0 0 10px rgba(255, 71, 87, 0.8);
        }
      }
    }
    
    .drop-zone {
      width: 300px;
      height: 150px;
      border: 3px dashed rgba(0, 255, 255, 0.3);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 255, 255, 0.05);
      transition: all 0.3s ease;
      
      &:hover {
        border-color: rgba(0, 255, 255, 0.6);
        background: rgba(0, 255, 255, 0.1);
      }
      
      .drop-zone-hint {
        color: rgba(0, 255, 255, 0.7);
        font-size: 14px;
        text-align: center;
        pointer-events: none;
      }
    }
  }
  
  // 玩家区域
  .player-area {
    flex: 0 0 35%;
    padding: 20px;
    border-top: 1px solid rgba(0, 255, 255, 0.2);
    
    .player-info {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 20px;
      
      .player-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, #00ffff, #0080ff);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        border: 2px solid rgba(0, 255, 255, 0.5);
      }
      
      .player-details {
        .player-name {
          color: #00ffff;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .health-bar {
          position: relative;
          width: 200px;
          height: 20px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 10px;
          overflow: hidden;
          border: 1px solid rgba(0, 255, 255, 0.3);
          
          .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #0080ff);
            transition: width 0.5s ease;
          }
          
          .health-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
          }
        }
      }
      
      .energy-display {
        .energy-count {
          background: rgba(255, 255, 0, 0.2);
          color: #ffff00;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 600;
          border: 1px solid rgba(255, 255, 0, 0.5);
        }
      }
    }
    
    .player-hand {
      display: flex;
      gap: 15px;
      justify-content: center;
      overflow-x: auto;
      padding: 10px 0;
    }
  }
}

// 战斗卡牌样式
.battle-card {
  width: 120px;
  height: 160px;
  background: linear-gradient(145deg, #2c3e50, #34495e);
  border-radius: 12px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  user-select: none;
  
  // 稀有度样式
  &.common {
    border: 2px solid rgba(169, 169, 169, 0.5);
    box-shadow: 0 4px 15px rgba(169, 169, 169, 0.2);
  }
  
  &.rare {
    border: 2px solid rgba(0, 150, 255, 0.5);
    box-shadow: 0 4px 15px rgba(0, 150, 255, 0.3);
  }
  
  &.epic {
    border: 2px solid rgba(128, 0, 255, 0.5);
    box-shadow: 0 4px 15px rgba(128, 0, 255, 0.3);
  }
  
  &.legendary {
    border: 2px solid rgba(255, 215, 0, 0.5);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    background: linear-gradient(145deg, #3c2e1e, #4d3a26);
  }
  
  &.selected {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.5);
    border-color: #00ffff;
  }
  
  .card-energy {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 24px;
    height: 24px;
    background: radial-gradient(circle, #ffff00, #ffa500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    color: #000;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .card-icon {
    text-align: center;
    font-size: 36px;
    margin: 10px 0;
  }
  
  .card-name {
    color: white;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 5px;
    line-height: 1.2;
  }
  
  .card-power {
    color: #ff4757;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 8px;
  }
  
  .card-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 9px;
    text-align: center;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .card-rarity-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    
    &.common { background: #a9a9a9; }
    &.rare { background: #0096ff; }
    &.epic { background: #8000ff; }
    &.legendary { background: #ffd700; }
  }
}

// HUD元素
.battle-hud {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
  
  .game-status {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 20px;
    pointer-events: auto;
    
    .phase-indicator {
      background: rgba(0, 0, 0, 0.8);
      color: #00ffff;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 1px;
      border: 1px solid rgba(0, 255, 255, 0.3);
    }
    
    .end-turn-btn {
      background: linear-gradient(45deg, #ff4757, #ff6b9d);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
      }
    }
  }
  
  .control-buttons {
    position: absolute;
    bottom: 20px;
    left: 20px;
    pointer-events: auto;
    
    .control-btn {
      background: rgba(0, 0, 0, 0.8);
      color: #00ffff;
      border: 1px solid rgba(0, 255, 255, 0.3);
      padding: 10px 20px;
      border-radius: 25px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      
      &:hover {
        background: rgba(0, 255, 255, 0.1);
        border-color: #00ffff;
      }
    }
  }
}

// 战斗结果弹窗
.battle-result-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  
  .result-panel {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    border: 2px solid rgba(0, 255, 255, 0.5);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    max-width: 400px;
    
    .result-icon {
      font-size: 80px;
      margin-bottom: 20px;
      
      &.player {
        filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
      }
      
      &.opponent {
        filter: drop-shadow(0 0 20px rgba(255, 71, 87, 0.8));
      }
    }
    
    .result-title {
      color: white;
      font-size: 36px;
      font-weight: 700;
      margin-bottom: 15px;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    }
    
    .result-message {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      margin-bottom: 30px;
      line-height: 1.5;
    }
    
    .result-actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      
      .result-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.primary {
          background: linear-gradient(45deg, #00ffff, #0080ff);
          color: #000;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
          }
        }
        
        &.secondary {
          background: transparent;
          color: #00ffff;
          border: 2px solid #00ffff;
          
          &:hover {
            background: rgba(0, 255, 255, 0.1);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// 动画定义
@keyframes grid-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.1; }
}

@keyframes energy-flow {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

@keyframes particles-drift {
  0% { transform: translate(0, 0); }
  33% { transform: translate(-30px, -30px); }
  66% { transform: translate(30px, -60px); }
  100% { transform: translate(0, 0); }
}

@keyframes card-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 移动端适配
@media (max-width: 768px) {
  .battle-interface {
    .opponent-area,
    .player-area {
      padding: 15px;
    }
    
    .battlefield {
      padding: 15px;
      
      .drop-zone {
        width: 250px;
        height: 120px;
      }
      
      .turn-indicator {
        font-size: 14px;
        padding: 8px 16px;
      }
    }
    
    .player-hand {
      gap: 10px;
      
      .battle-card {
        width: 100px;
        height: 140px;
        
        .card-icon {
          font-size: 30px;
        }
        
        .card-name {
          font-size: 11px;
        }
      }
    }
    
    .opponent-cards .card-back {
      width: 70px;
      height: 95px;
    }
  }
  
  .battle-result-overlay .result-panel {
    padding: 30px;
    margin: 20px;
    
    .result-icon {
      font-size: 60px;
    }
    
    .result-title {
      font-size: 28px;
    }
    
    .result-actions {
      flex-direction: column;
      
      .result-btn {
        width: 100%;
      }
    }
  }
}