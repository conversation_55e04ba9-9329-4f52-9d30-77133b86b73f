import React, { useState, useEffect } from 'react';
import { useUIState } from '../store/hooks';
import type { GameScene } from '../types/game';
import { useAuth } from '../store/hooks/useAuth';
import { LoginModal } from './components/auth/LoginModal';

// 直接导入场景组件，避免懒加载
import WorldMapScene from './scenes/WorldMapScene';
import AdventureScene from './scenes/AdventureScene';
import ArenaScene from './scenes/ArenaScene';
import BattleScene from './scenes/BattleScene';
import StrategyScene from './scenes/StrategyCreationScene';
import StrategyOptimizationScene from './scenes/StrategyOptimizationScene';
import ShopScene from './scenes/ShopScene';
import ProfileScene from './scenes/ProfileScene';
import SettingsScene from './scenes/SettingsScene';
import BacktestScene from './scenes/BacktestScene';
import CodexPanel from './components/codexpanel';
import CardDrawScene from './scenes/CardDrawScene';

// 场景组件映射
const sceneComponents: Record<GameScene, React.ComponentType<any>> = {
  WorldMap: WorldMapScene,
  Adventure: AdventureScene,
  Arena: ArenaScene,
  Battle: BattleScene,
  Codex: CodexPanel,
  CardDraw: CardDrawScene,
  Strategy: StrategyScene,
  Monitor: StrategyOptimizationScene,
  StrategyCreation: StrategyScene,
  StrategyOptimization: StrategyOptimizationScene,
  Shop: ShopScene,
  Profile: ProfileScene,
  Settings: SettingsScene,
  Backtest: BacktestScene
};

// 需要认证的场景列表
const PROTECTED_SCENES: GameScene[] = [
  'Adventure', 'Arena', 'Battle', 'Codex', 'CardDraw', 'Strategy', 'Monitor',
  'StrategyCreation', 'StrategyOptimization', 'Shop', 'Profile',
  'Settings', 'Backtest'
];

/**
 * 场景路由器组件 - 支持访问控制和自动登录
 */
export default function SceneRouter() {
  const { currentScene, switchScene } = useUIState();
  const { isAuthenticated, isGuest } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [pendingScene, setPendingScene] = useState<GameScene | null>(null);

  // 监听场景切换，检查访问权限
  useEffect(() => {
    const isLocked = !isAuthenticated || isGuest;
    const isProtectedScene = PROTECTED_SCENES.includes(currentScene);
    
    if (isLocked && isProtectedScene) {
      // 访客尝试访问受保护场景，记录目标场景并显示登录模态框
      console.log(`🔐 访客尝试访问受保护场景: ${currentScene}，显示登录模态框`);
      setPendingScene(currentScene);
      setShowLoginModal(true);
      
      // 暂时切换回世界地图
      switchScene('WorldMap');
    }
  }, [currentScene, isAuthenticated, isGuest, switchScene]);

  // 登录成功后的处理
  const handleLoginSuccess = () => {
    console.log(`✅ 登录成功，准备跳转到目标场景: ${pendingScene}`);
    setShowLoginModal(false);
    
    // 如果有待跳转的场景，跳转过去
    if (pendingScene) {
      setTimeout(() => {
        console.log(`🚀 正在跳转到: ${pendingScene}`);
        switchScene(pendingScene);
        setPendingScene(null);
      }, 100);
    }
  };

  // 取消登录的处理
  const handleLoginCancel = () => {
    console.log('❌ 用户取消登录');
    setShowLoginModal(false);
    setPendingScene(null);
  };

  // 决定要渲染的场景
  const isLocked = !isAuthenticated || isGuest;
  const guardedScene: GameScene = isLocked && PROTECTED_SCENES.includes(currentScene) 
    ? 'WorldMap' 
    : currentScene;
  
  const SceneComponent = sceneComponents[guardedScene];

  return (
    <>
      <SceneComponent />
      
      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={handleLoginCancel}
        onSuccess={handleLoginSuccess}
        title="需要登录"
        subtitle={pendingScene ? `访问 ${getSceneName(pendingScene)} 需要登录账户` : "请登录以继续"}
        showGuestOption={true}
      />
    </>
  );
}

// 获取场景的友好名称
function getSceneName(scene: GameScene): string {
  const sceneNames: Record<GameScene, string> = {
    WorldMap: '世界地图',
    Adventure: '冒险模式',
    Arena: '竞技场',
    Battle: '战斗场景',
    Codex: '策略博物馆',
    Strategy: '策略工坊',
    Monitor: '策略监控',
    StrategyCreation: '策略创建',
    StrategyOptimization: '策略优化',
    Shop: '商店',
    Profile: '个人资料',
    Settings: '设置',
    Backtest: '回测分析'
  };

  return sceneNames[scene] || scene;
}