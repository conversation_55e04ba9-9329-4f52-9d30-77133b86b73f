import React from 'react';
import { useSceneNavigation } from '../SceneRouter';

export default function CollectionScene({ sceneData }: { sceneData?: any }) {
  const navigation = useSceneNavigation();

  return (
    <div style={{ padding: '2rem', textAlign: 'center', color: 'white' }}>
      <h1>🃏 Card Collection</h1>
      <p>Your strategy cards will appear here</p>
      <button 
        onClick={() => navigation.goToWorldMap()}
        style={{ padding: '12px 24px', marginTop: '2rem', background: 'transparent', color: '#00ffff', border: '2px solid #00ffff', borderRadius: '25px', cursor: 'pointer' }}
      >
        ← Back to Map
      </button>
    </div>
  );
}