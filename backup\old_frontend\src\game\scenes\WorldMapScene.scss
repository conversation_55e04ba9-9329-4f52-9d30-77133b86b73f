@use "../../styles/themes/cyber-theme" as cyber;

.world-map-scene {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: radial-gradient(ellipse at center, rgba(0, 40, 80, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
}

// 地图容器
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  
  // 地图背景
  .map-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    
    .background-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: grid-shift 20s linear infinite;
      
      @keyframes grid-shift {
        0% { transform: translate(0, 0); }
        100% { transform: translate(50px, 50px); }
      }
    }
    
    .terrain-layer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      
      .terrain-svg {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  // 地图交互点
  .map-points {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
  }
  
  // 中央logo
  .map-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    
    .center-logo {
      .logo-ring {
        position: relative;
        width: 120px;
        height: 120px;
        border: 2px solid rgba(0, 255, 255, 0.5);
        border-radius: 50%;
        background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
        animation: logo-pulse 3s ease-in-out infinite;
        
        &::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          border-radius: 50%;
          border: 1px solid rgba(255, 255, 255, 0.2);
          animation: logo-rotate 10s linear infinite;
        }
        
        .logo-inner {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #00ffff;
          text-align: center;
          
          .logo-text {
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 2px;
          }
          
          .logo-subtitle {
            font-size: 10px;
            opacity: 0.7;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
}

// 地图交互点样式
.map-point {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 10;
  
  // 光环效果
  .point-aura {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    opacity: 0;
    transition: all 0.3s ease;
  }
  
  // 图标容器
  .point-icon {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(0, 150, 255, 0.3));
    border: 2px solid rgba(0, 255, 255, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
    transition: all 0.3s ease;
  }
  
  // 脉冲效果
  .point-pulse {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid rgba(0, 255, 255, 0.8);
    top: 0;
    left: 0;
    animation: point-pulse 2s ease-in-out infinite;
    opacity: 0.6;
  }
  
  // 标签
  .point-label {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    color: #00ffff;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }
  
  // 连接线
  .point-connection {
    position: absolute;
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, rgba(0, 255, 255, 0.6), transparent);
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0.3;
  }
  
  // 解锁状态
  &.unlocked {
    &:hover {
      .point-aura {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
      }
      
      .point-icon {
        transform: scale(1.1);
        box-shadow: 0 6px 30px rgba(0, 255, 255, 0.5);
      }
      
      .point-label {
        opacity: 1;
        color: #ffff00;
      }
    }
  }
  
  // 锁定状态
  &.locked {
    cursor: not-allowed;
    
    .point-icon {
      background: linear-gradient(45deg, rgba(100, 100, 100, 0.2), rgba(150, 150, 150, 0.3));
      border-color: rgba(150, 150, 150, 0.4);
      color: #666;
      box-shadow: 0 2px 10px rgba(100, 100, 100, 0.2);
      
      &::after {
        content: '🔒';
        position: absolute;
        font-size: 16px;
      }
    }
    
    .point-pulse {
      display: none;
    }
    
    .point-label {
      color: #666;
    }
  }
  
  // 选中状态
  &.selected {
    .point-aura {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.5);
      animation: selected-pulse 1s ease-in-out infinite;
    }
    
    .point-icon {
      background: linear-gradient(45deg, rgba(255, 255, 0, 0.3), rgba(255, 150, 0, 0.4));
      border-color: #ffff00;
      box-shadow: 0 8px 40px rgba(255, 255, 0, 0.6);
      animation: selected-glow 1s ease-in-out infinite alternate;
    }
  }
}

// 信息面板
.info-panel {
  position: absolute;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  width: 280px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.5);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.2);
  z-index: 100;
  
  .panel-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .panel-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(45deg, rgba(0, 255, 255, 0.2), rgba(0, 150, 255, 0.3));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      margin-right: 12px;
    }
    
    .panel-title {
      color: #00ffff;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
    }
  }
  
  .panel-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
  }
  
  .panel-actions {
    .enter-btn {
      width: 100%;
      padding: 12px;
      background: linear-gradient(45deg, #00ffff, #0080ff);
      color: #000;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
      }
    }
  }
}

// HUD元素
.world-map-hud {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 50;
  
  // 玩家信息
  .player-info {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px 15px;
    border-radius: 25px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    pointer-events: auto;
    
    .player-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(45deg, #00ffff, #0080ff);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      margin-right: 10px;
    }
    
    .player-details {
      .player-name {
        color: #00ffff;
        font-size: 14px;
        font-weight: 600;
      }
      
      .player-level {
        color: rgba(255, 255, 255, 0.7);
        font-size: 12px;
      }
    }
  }
  
  // 快捷操作
  .quick-actions {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    
    .quick-btn {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.8);
      border: 1px solid rgba(0, 255, 255, 0.3);
      color: #00ffff;
      font-size: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      pointer-events: auto;
      
      &:hover {
        background: rgba(0, 255, 255, 0.1);
        border-color: #00ffff;
        transform: scale(1.1);
      }
    }
  }
}

// 浮动粒子
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  
  .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ffff;
    border-radius: 50%;
    animation: particle-float linear infinite;
    opacity: 0.6;
    
    &:nth-child(odd) {
      background: #ff00ff;
    }
    
    &:nth-child(3n) {
      background: #ffff00;
    }
  }
}

// 动画定义
@keyframes point-pulse {
  0%, 100% { 
    transform: scale(1); 
    opacity: 0.6; 
  }
  50% { 
    transform: scale(1.2); 
    opacity: 0.3; 
  }
}

@keyframes selected-pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes selected-glow {
  0% { box-shadow: 0 8px 40px rgba(255, 255, 0, 0.6); }
  100% { box-shadow: 0 12px 60px rgba(255, 255, 0, 0.8); }
}

@keyframes logo-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes logo-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-10px) translateX(50px);
    opacity: 0;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .info-panel {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    top: auto;
    transform: none;
    width: auto;
  }
  
  .world-map-hud {
    .player-info {
      top: 10px;
      left: 10px;
      padding: 8px 12px;
      
      .player-avatar {
        width: 35px;
        height: 35px;
        font-size: 18px;
      }
    }
    
    .quick-actions {
      top: 10px;
      right: 10px;
      
      .quick-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
      }
    }
  }
  
  .map-point {
    .point-icon {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
    
    .point-pulse {
      width: 50px;
      height: 50px;
    }
    
    .point-aura {
      width: 70px;
      height: 70px;
    }
    
    .point-label {
      top: 60px;
      font-size: 11px;
    }
  }
  
  .map-center {
    .center-logo .logo-ring {
      width: 100px;
      height: 100px;
      
      .logo-inner {
        .logo-text {
          font-size: 14px;
        }
        
        .logo-subtitle {
          font-size: 9px;
        }
      }
    }
  }
}